package data

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"time"

	cmncontext "common/context"
	"common/database"
	"common/observability"

	"github.com/getsentry/sentry-go"
)

const (
	// Sentry span operation constants
	dbQueryOperation = "db.query"
)

// postgresCallQueueRepository is a PostgreSQL implementation of CallQueueRepository
type postgresCallQueueRepository struct {
	db            *sql.DB
	queueStrategy QueueStrategy
}

// NewPostgresCallQueueRepository creates a new PostgreSQL-backed call queue repository
func NewPostgresCallQueueRepository(db *sql.DB) CallQueueRepository {
	return &postgresCallQueueRepository{
		db: db,
	}
}

// Helper to scan a single QueuedCall from a scannable source
func scanSingleQueuedCall(s interface{ Scan(...interface{}) error }) (QueuedCall, error) {
	var (
		call           QueuedCall
		attributesJSON []byte
		assetID        sql.NullString
		callerName     sql.NullString
		situationID    sql.NullString
	)
	err := s.Scan(
		&call.CallSID,
		&call.Caller,
		&callerName,
		&call.EnqueueTime,
		&assetID,
		&situationID,
		&call.State,
		&call.Direction,
		&attributesJSON,
		&call.CallStartTime,
		&call.CallEndTime,
		&call.LastHoldStart,
	)
	if err != nil {
		return QueuedCall{}, err
	}

	if assetID.Valid {
		call.AssetID = assetID.String
	}
	if callerName.Valid {
		call.CallerName = callerName.String
	} else {
		call.CallerName = "" // Default for NULL
	}
	if situationID.Valid {
		call.SituationID = situationID.String
	} else {
		call.SituationID = "" // Default for NULL
	}

	if len(attributesJSON) > 0 && string(attributesJSON) != "null" {
		err = json.Unmarshal(attributesJSON, &call.Attributes)
		if err != nil {
			return QueuedCall{}, fmt.Errorf("failed to parse attributes: %w", err)
		}
	} else {
		call.Attributes = make(map[string]string)
	}

	return call, nil
}

// Helper function for *sql.Row
func scanQueuedCall(row *sql.Row) (QueuedCall, error) {
	return scanSingleQueuedCall(row)
}

// Helper function for *sql.Rows
func scanQueuedCalls(rows *sql.Rows) ([]QueuedCall, error) {
	var calls []QueuedCall
	defer rows.Close()
	for rows.Next() {
		call, err := scanSingleQueuedCall(rows)
		if err != nil {
			return nil, err
		}
		calls = append(calls, call)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return calls, nil
}

func (r *postgresCallQueueRepository) GetOrgTwilioDetails(ctx context.Context) (OrgTwilioDetails, error) {
	span, cleanup := observability.StartSpanWithTransaction(ctx, "db.get_org_twilio_details", "CallQueue.GetOrgTwilioDetails")
	defer cleanup()
	if span != nil {
		span.Op = dbQueryOperation
		span.SetTag("db.operation", "SELECT")
		span.SetTag("db.table", "orgs")
		span.Description = "CallQueue.GetOrgTwilioDetails"
	}

	return database.WithSession(r.db, ctx, nil, func(tx *sql.Tx) (OrgTwilioDetails, error) {
		orgId := cmncontext.GetOrgId(ctx)
		if span != nil {
			span.SetTag("org_id", fmt.Sprintf("%d", orgId))
		}

		row := tx.QueryRowContext(ctx, `
			SELECT twiml_app_sid, twilio_number, twilio_api_user_id, name
			FROM orgs
			where id = $1
		`, orgId)

		var twimlAppSid, twilioNumber, orgName string
		var twilioApiUserId sql.NullString
		err := row.Scan(&twimlAppSid, &twilioNumber, &twilioApiUserId, &orgName)
		if err != nil {
			if span != nil {
				span.SetTag("error", "true")
				span.SetTag("db.rows_affected", "0")
			}
			sentry.CaptureException(err)
			return OrgTwilioDetails{}, fmt.Errorf("failed to get org twilio details: %w", err)
		}

		if span != nil {
			span.SetTag("db.rows_affected", "1")
		}

		details := OrgTwilioDetails{
			TwimlAppSid:  twimlAppSid,
			TwilioNumber: twilioNumber,
			OrgName:      orgName,
		}

		log.Println("GetOrgTwilioDetails twilioApiUserId", twilioApiUserId, twilioApiUserId.String)

		if twilioApiUserId.Valid {
			details.TwilioApiUserId = twilioApiUserId.String
		} else {
			details.TwilioApiUserId = "" // Assign empty string if NULL
		}

		return details, nil
	})
}

// EnqueueCall adds a call to the waiting queue
func (r *postgresCallQueueRepository) EnqueueCall(ctx context.Context, call QueuedCall) error {
	span, cleanup := observability.StartSpanWithTransaction(ctx, "db.enqueue_call", "CallQueue.EnqueueCall")
	defer cleanup()
	if span != nil {
		span.Op = dbQueryOperation
		span.Description = "CallQueue.EnqueueCall"
		span.SetTag("db.operation", "INSERT")
		span.SetTag("db.table", "call_queue")
		span.SetTag("call_sid", call.CallSID)
		span.SetTag("caller", call.Caller)
		span.SetTag("direction", call.Direction)
	}

	return database.WithSessionErr(r.db, ctx, nil, func(tx *sql.Tx) error {
		// Get org ID from context
		orgID := cmncontext.GetOrgId(ctx)

		// Validate inputs
		if call.CallSID == "" {
			return ErrInvalidInput
		}

		// Set enqueue time if not already set
		if call.EnqueueTime.IsZero() {
			call.EnqueueTime = time.Now()
		}

		// Set default direction to inbound if not provided
		if call.Direction == "" {
			call.Direction = CallDirectionInbound
		}

		// Set state to waiting
		call.State = CallStateWaiting

		// Convert attributes to JSON
		attributesJSON, err := json.Marshal(call.Attributes)
		if err != nil {
			return fmt.Errorf("failed to marshal attributes: %w", err)
		}

		log.Printf("EnqueueCall: %+v", call)

		var query string
		var args []interface{}

		// WARNING: If we try to insert AssetID as "", the insert query will SILENTLY fail
		if call.AssetID != "" {
			query = `
				INSERT INTO call_queue (
					call_sid, org_id, 
					caller, caller_name, asset_id, enqueue_time,
					situation_id, state, direction, attributes,
					call_start_time, call_end_time, last_hold_start
				) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
			`
			args = []interface{}{
				call.CallSID,
				orgID,
				call.Caller,
				call.CallerName,
				call.AssetID,
				call.EnqueueTime,
				call.SituationID,
				call.State,
				call.Direction,
				attributesJSON,
				call.CallStartTime,
				call.CallEndTime,
				call.LastHoldStart,
			}
		} else {
			query = `
				INSERT INTO call_queue (
					call_sid, org_id, 
					caller, caller_name, enqueue_time,
					situation_id, state, direction, attributes,
					call_start_time, call_end_time, last_hold_start
				) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
			`
			args = []interface{}{
				call.CallSID,
				orgID,
				call.Caller,
				call.CallerName,
				call.EnqueueTime,
				call.SituationID,
				call.State,
				call.Direction,
				attributesJSON,
				call.CallStartTime,
				call.CallEndTime,
				call.LastHoldStart,
			}
		}

		preparedStatement, err := tx.PrepareContext(ctx, query)
		if err != nil {
			return err
		}
		defer preparedStatement.Close()

		_, executionError := preparedStatement.ExecContext(ctx, args...)
		if executionError != nil {
			if span != nil {
				span.SetTag("error", "true")
				span.SetTag("db.rows_affected", "0")
			}
			sentry.CaptureException(executionError)
			return fmt.Errorf("failed to insert call: %w", executionError)
		}

		if span != nil {
			span.SetTag("db.rows_affected", "1")
		}
		log.Printf("COMPLETED Enqueued call: %+v", call)

		return nil
	})
}

// DequeueCall gets the next call from the queue and assigns it to an asset
func (r *postgresCallQueueRepository) DequeueCall(ctx context.Context, assetID string) (QueuedCall, bool, error) {
	var call QueuedCall
	var found bool

	err := database.WithSessionErr(r.db, ctx, nil, func(tx *sql.Tx) error {
		if assetID == "" {
			return ErrInvalidInput
		}

		var row *sql.Row

		// Get next call based on strategy or default FIFO
		if r.queueStrategy != nil {
			// Get all waiting calls to apply strategy
			rows, err := tx.QueryContext(ctx, `
				SELECT call_sid, caller, caller_name, enqueue_time, 
					asset_id, situation_id, state, direction, attributes,
					call_start_time, call_end_time, last_hold_start
				FROM call_queue 
				WHERE state = $1
				ORDER BY enqueue_time ASC
			`, CallStateWaiting)
			if err != nil {
				return fmt.Errorf("failed to query waiting calls: %w", err)
			}

			waitingCalls, err := scanQueuedCalls(rows)
			if err != nil {
				return fmt.Errorf("failed to scan waiting calls: %w", err)
			}

			if len(waitingCalls) == 0 {
				return nil // No calls found
			}

			// Apply strategy
			selectedCall, _, ok, errStrategy := r.queueStrategy.SelectNextCall(ctx, waitingCalls)
			if errStrategy != nil {
				return fmt.Errorf("strategy selection failed: %w", errStrategy)
			}
			if !ok {
				return nil // Strategy didn't select a call
			}
			call = selectedCall
			found = true

		} else {
			// Default FIFO behavior
			row = tx.QueryRowContext(ctx, `
				SELECT call_sid, caller, caller_name, enqueue_time, 
					asset_id, situation_id, state, direction, attributes,
					call_start_time, call_end_time, last_hold_start
			FROM call_queue 
				WHERE state = $1
				ORDER BY enqueue_time ASC
				LIMIT 1
				FOR UPDATE SKIP LOCKED
			`, CallStateWaiting)

			var scanErr error
			call, scanErr = scanQueuedCall(row)
			if scanErr == sql.ErrNoRows {
				return nil // No calls found
			}
			if scanErr != nil {
				return fmt.Errorf("failed to scan call: %w", scanErr)
			}
			found = true
		}

		// Update the call to assign it to the asset and set start time
		call.AssetID = assetID
		call.State = CallStateActive

		attributesJSON, err := json.Marshal(call.Attributes)
		if err != nil {
			return fmt.Errorf("failed to marshal attributes: %w", err)
		}

		_, err = tx.ExecContext(ctx, `
			UPDATE call_queue 
			SET asset_id = $1, state = $2, attributes = $3, call_start_time = NOW()
			WHERE call_sid = $4
		`, assetID, CallStateActive, attributesJSON, call.CallSID)
		if err != nil {
			return fmt.Errorf("failed to update call: %w", err)
		}

		// Re-fetch to get the DB-set CallStartTime
		rowAfterUpdate := tx.QueryRowContext(ctx, `
			SELECT call_sid, caller, caller_name, enqueue_time, 
				asset_id, situation_id, state, direction, attributes,
				call_start_time, call_end_time, last_hold_start
			FROM call_queue WHERE call_sid = $1`, call.CallSID)
		updatedCall, scanErrAfterUpdate := scanQueuedCall(rowAfterUpdate)
		if scanErrAfterUpdate != nil {
			return fmt.Errorf("failed to re-scan call after update: %w", scanErrAfterUpdate)
		}
		call = updatedCall

		return nil
	})

	if err != nil {
		return QueuedCall{}, false, err
	}

	return call, found, nil
}

// HoldCall places an active call on hold
func (r *postgresCallQueueRepository) HoldCall(ctx context.Context, callSID string, assetID string) error {
	span, cleanup := observability.StartSpanWithTransaction(ctx, "db.hold_call", "CallQueue.HoldCall")
	defer cleanup()
	if span != nil {
		span.Op = "db.transaction"
		span.Description = "CallQueue.HoldCall"
		span.SetTag("db.operation", "SELECT_UPDATE")
		span.SetTag("db.table", "call_queue")
		span.SetTag("call_sid", callSID)
		span.SetTag("asset_id", assetID)
	}

	return database.WithSessionErr(r.db, ctx, nil, func(tx *sql.Tx) error {
		if callSID == "" || assetID == "" {
			if span != nil {
				span.SetTag("error", "true")
				span.SetTag("error.type", "validation")
			}
			sentry.CaptureException(ErrInvalidInput)
			return ErrInvalidInput
		}

		// Check if this is the asset's active call and lock the row
		row := tx.QueryRowContext(ctx, `
			SELECT call_sid, caller, caller_name, enqueue_time, 
				asset_id, situation_id, state, direction, attributes,
				call_start_time, call_end_time, last_hold_start
			FROM call_queue 
			WHERE call_sid = $1 AND asset_id = $2 AND state = $3
			FOR UPDATE
		`, callSID, assetID, CallStateActive)

		_, err := scanQueuedCall(row)
		if err == sql.ErrNoRows {
			if span != nil {
				span.SetTag("error", "true")
				span.SetTag("error.type", "business_logic")
				span.SetTag("db.rows_affected", "0")
			}
			sentry.CaptureException(ErrNoActiveCall)
			return ErrNoActiveCall
		}
		if err != nil {
			if span != nil {
				span.SetTag("error", "true")
				span.SetTag("error.type", "database")
			}
			sentry.CaptureException(err)
			return fmt.Errorf("failed to get active call for hold: %w", err)
		}

		// Update state to hold and set last_hold_start time
		_, err = tx.ExecContext(ctx, `
			UPDATE call_queue 
			SET state = $1, last_hold_start = NOW()
			WHERE call_sid = $2
		`, CallStateHold, callSID)
		if err != nil {
			if span != nil {
				span.SetTag("error", "true")
				span.SetTag("db.rows_affected", "0")
			}
			sentry.CaptureException(err)
			return fmt.Errorf("failed to update call state to hold: %w", err)
		}

		if span != nil {
			span.SetTag("db.rows_affected", "1")
		}
		return nil
	})
}

// ResumeCall takes a call off hold
func (r *postgresCallQueueRepository) ResumeCall(ctx context.Context, callSID string, assetID string) error {
	span, cleanup := observability.StartSpanWithTransaction(ctx, "db.resume_call", "CallQueue.ResumeCall")
	defer cleanup()
	if span != nil {
		span.Op = "db.transaction"
		span.Description = "CallQueue.ResumeCall"
		span.SetTag("db.operation", "SELECT_UPDATE")
		span.SetTag("db.table", "call_queue")
		span.SetTag("call_sid", callSID)
		span.SetTag("asset_id", assetID)
	}

	return database.WithSessionErr(r.db, ctx, nil, func(tx *sql.Tx) error {
		if callSID == "" || assetID == "" {
			if span != nil {
				span.SetTag("error", "true")
				span.SetTag("error.type", "validation")
			}
			sentry.CaptureException(ErrInvalidInput)
			return ErrInvalidInput
		}

		// Check if the call is on hold for this asset
		row := tx.QueryRowContext(ctx, `
			SELECT call_sid, caller, caller_name, enqueue_time, 
				asset_id, situation_id, state, direction, attributes,
				call_start_time, call_end_time, last_hold_start
			FROM call_queue 
			WHERE call_sid = $1 AND asset_id = $2 AND state = $3
			FOR UPDATE
		`, callSID, assetID, CallStateHold)

		_, err := scanQueuedCall(row)
		if err == sql.ErrNoRows {
			if span != nil {
				span.SetTag("error", "true")
				span.SetTag("error.type", "business_logic")
				span.SetTag("db.rows_affected", "0")
			}
			sentry.CaptureException(ErrCallNotFound)
			return ErrCallNotFound
		}
		if err != nil {
			if span != nil {
				span.SetTag("error", "true")
				span.SetTag("error.type", "database")
			}
			sentry.CaptureException(err)
			return fmt.Errorf("failed to get held call for resume: %w", err)
		}

		// Update state to active and clear last_hold_start
		_, err = tx.ExecContext(ctx, `
			UPDATE call_queue 
			SET state = $1, last_hold_start = NULL
			WHERE call_sid = $2
		`, CallStateActive, callSID)
		if err != nil {
			if span != nil {
				span.SetTag("error", "true")
				span.SetTag("db.rows_affected", "0")
			}
			sentry.CaptureException(err)
			return fmt.Errorf("failed to update call state to active: %w", err)
		}

		if span != nil {
			span.SetTag("db.rows_affected", "1")
		}
		return nil
	})
}

// RevertSelectiveClaim reverts a call from pending_selective_assignment back to waiting state
func (r *postgresCallQueueRepository) RevertSelectiveClaim(ctx context.Context, callSID string) (bool, error) {
	return database.WithSession(r.db, ctx, nil, func(tx *sql.Tx) (bool, error) {
		if callSID == "" {
			return false, ErrInvalidInput
		}

		// Update state to waiting and clear asset_id only if currently in pending_selective_assignment state
		result, err := tx.ExecContext(ctx, `
			UPDATE call_queue 
			SET state = $1, asset_id = NULL
			WHERE call_sid = $2 AND state = $3
		`, CallStateWaiting, callSID, CallStatePendingSelectiveAssign)
		if err != nil {
			return false, fmt.Errorf("failed to revert call state: %w", err)
		}

		// Check if any rows were affected
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return true, fmt.Errorf("could not get rows affected on RevertSelectiveClaim for %s: %w", callSID, err)
		}

		// Return true if a row was updated, false otherwise (idempotent)
		return rowsAffected > 0, nil
	})
}

// EndCall terminates a call in any state
func (r *postgresCallQueueRepository) EndCall(ctx context.Context, callSID string) error {
	span, cleanup := observability.StartSpanWithTransaction(ctx, "db.end_call", "CallQueue.EndCall")
	defer cleanup()
	if span != nil {
		span.Op = "db.query"
		span.Description = "CallQueue.EndCall"
		span.SetTag("db.operation", "UPDATE")
		span.SetTag("db.table", "call_queue")
		span.SetTag("call_sid", callSID)
	}

	return database.WithSessionErr(r.db, ctx, nil, func(tx *sql.Tx) error {
		if callSID == "" {
			if span != nil {
				span.SetTag("error", "true")
				span.SetTag("error.type", "validation")
			}
			sentry.CaptureException(ErrInvalidInput)
			return ErrInvalidInput
		}

		// Update state to ended and set call_end_time
		result, err := tx.ExecContext(ctx, `
			UPDATE call_queue 
			SET state = $1, call_end_time = NOW()
			WHERE call_sid = $2
		`, CallStateEnded, callSID)
		if err != nil {
			if span != nil {
				span.SetTag("error", "true")
				span.SetTag("db.rows_affected", "0")
			}
			sentry.CaptureException(err)
			return fmt.Errorf("failed to update call state to ended: %w", err)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			log.Printf("Warning: could not get rows affected on EndCall for %s: %v", callSID, err)
		}
		if rowsAffected == 0 {
			if span != nil {
				span.SetTag("error", "true")
				span.SetTag("db.rows_affected", "0")
			}
			sentry.CaptureException(ErrCallNotFound)
			return ErrCallNotFound
		}

		if span != nil {
			span.SetTag("db.rows_affected", fmt.Sprintf("%d", rowsAffected))
		}
		return nil
	})
}

// GetActiveCall returns the current active call for an asset
func (r *postgresCallQueueRepository) GetActiveCall(ctx context.Context, assetID string) (QueuedCall, bool, error) {
	span, cleanup := observability.StartSpanWithTransaction(ctx, "db.get_active_call", "CallQueue.GetActiveCall")
	defer cleanup()
	if span != nil {
		span.Op = "db.query"
		span.Description = "CallQueue.GetActiveCall"
		span.SetTag("db.operation", "SELECT")
		span.SetTag("db.table", "call_queue")
		span.SetTag("asset_id", assetID)
	}

	var call QueuedCall
	var found bool

	err := database.WithSessionErr(r.db, ctx, nil, func(tx *sql.Tx) error {
		if assetID == "" {
			if span != nil {
				span.SetTag("error", "true")
				span.SetTag("error.type", "validation")
			}
			sentry.CaptureException(ErrInvalidInput)
			return ErrInvalidInput
		}

		row := tx.QueryRowContext(ctx, `
			SELECT call_sid, caller, caller_name, enqueue_time, 
				asset_id, situation_id, state, direction, attributes,
				call_start_time, call_end_time, last_hold_start
			FROM call_queue 
			WHERE asset_id = $1 AND state = $2
		`, assetID, CallStateActive)

		var err error
		call, err = scanQueuedCall(row)
		if err == sql.ErrNoRows {
			if span != nil {
				span.SetTag("db.rows_affected", "0")
			}
			return nil
		}
		if err != nil {
			if span != nil {
				span.SetTag("error", "true")
				span.SetTag("error.type", "database")
			}
			sentry.CaptureException(err)
			return fmt.Errorf("failed to scan call: %w", err)
		}
		found = true
		if span != nil {
			span.SetTag("db.rows_affected", "1")
			span.SetTag("call_sid", call.CallSID)
		}
		return nil
	})

	if err != nil {
		if span != nil {
			span.SetTag("error", "true")
		}
		sentry.CaptureException(err)
		return QueuedCall{}, false, err
	}

	return call, found, nil
}

// GetHeldCalls returns all calls on hold for an asset
func (r *postgresCallQueueRepository) GetHeldCalls(ctx context.Context, assetID string) ([]QueuedCall, error) {
	var calls []QueuedCall

	err := database.WithSessionErr(r.db, ctx, nil, func(tx *sql.Tx) error {
		if assetID == "" {
			return ErrInvalidInput
		}

		rows, err := tx.QueryContext(ctx, `
			SELECT call_sid, caller, caller_name, enqueue_time, 
				asset_id, situation_id, state, direction, attributes,
				call_start_time, call_end_time, last_hold_start
			FROM call_queue 
			WHERE asset_id = $1 AND state = $2
			ORDER BY enqueue_time ASC
		`, assetID, CallStateHold)
		if err != nil {
			return fmt.Errorf("failed to query held calls: %w", err)
		}
		defer rows.Close()

		calls, err = scanQueuedCalls(rows)
		if err != nil {
			return fmt.Errorf("failed to scan held calls: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return calls, nil
}

// GetCallBySituationID finds a call by its associated situation
func (r *postgresCallQueueRepository) GetCallBySituationID(ctx context.Context, situationID string) (QueuedCall, bool, error) {
	var call QueuedCall
	var found bool

	err := database.WithSessionErr(r.db, ctx, nil, func(tx *sql.Tx) error {
		if situationID == "" {
			return ErrInvalidInput
		}

		row := tx.QueryRowContext(ctx, `
			SELECT call_sid, caller, caller_name, enqueue_time, 
				asset_id, situation_id, state, direction, attributes,
				call_start_time, call_end_time, last_hold_start
			FROM call_queue 
			WHERE situation_id = $1
		`, situationID)

		var err error
		call, err = scanQueuedCall(row)
		if err == sql.ErrNoRows {
			return nil
		}
		if err != nil {
			return fmt.Errorf("failed to scan call: %w", err)
		}
		found = true
		return nil
	})

	if err != nil {
		return QueuedCall{}, false, err
	}

	return call, found, nil
}

// GetQueueStatus returns current queue information
// Note: This operation is frequently polled, so we use 10% sampling to reduce observability overhead
func (r *postgresCallQueueRepository) GetQueueStatus(ctx context.Context) (int, []QueuedCall, error) {
	// Sample this frequent operation at 10% to reduce noise
	span, cleanup := observability.StartSampledSpan(ctx, "db.get_queue_status", "CallQueue.GetQueueStatus", 0.1)
	if span != nil {
		span.Op = dbQueryOperation
		span.SetTag("db.operation", "SELECT")
		span.SetTag("db.table", "call_queue")
		span.Description = "CallQueue.GetQueueStatus"
	}
	defer cleanup()

	var queueSize int
	var waitingCalls []QueuedCall

	err := database.WithSessionErr(r.db, ctx, nil, func(tx *sql.Tx) error {
		// Get queue size - only create child span if parent is being traced
		var countSpan *sentry.Span
		var countCleanup func()
		if span != nil {
			countSpan, countCleanup = observability.StartSpanWithTransaction(ctx, "db.count_waiting_calls", "CallQueue.CountWaitingCalls")
			countSpan.Op = dbQueryOperation
			countSpan.SetTag("db.operation", "COUNT")
			countSpan.SetTag("db.table", "call_queue")
		} else {
			countCleanup = func() {} // No-op cleanup
		}
		err := tx.QueryRowContext(ctx, `
			SELECT COUNT(*) 
			FROM call_queue 
			WHERE state = $1
		`, CallStateWaiting).Scan(&queueSize)
		if err != nil {
			if countSpan != nil {
				countSpan.SetTag("error", "true")
				sentry.CaptureException(err)
			}
			countCleanup()
			return fmt.Errorf("failed to get queue size: %w", err)
		}
		if countSpan != nil {
			countSpan.SetTag("queue_size", fmt.Sprintf("%d", queueSize))
		}
		countCleanup()

		// Get waiting calls - only create child span if parent is being traced
		var selectSpan *sentry.Span
		var selectCleanup func()
		if span != nil {
			selectSpan, selectCleanup = observability.StartSpanWithTransaction(ctx, "db.select_waiting_calls", "CallQueue.SelectWaitingCalls")
			selectSpan.Op = dbQueryOperation
			selectSpan.SetTag("db.operation", "SELECT")
			selectSpan.SetTag("db.table", "call_queue")
		} else {
			selectCleanup = func() {} // No-op cleanup
		}
		rows, err := tx.QueryContext(ctx, `
			SELECT call_sid, caller, caller_name, enqueue_time, 
				asset_id, situation_id, state, direction, attributes,
				call_start_time, call_end_time, last_hold_start
			FROM call_queue 
			WHERE state = $1
			ORDER BY enqueue_time ASC
		`, CallStateWaiting)
		if err != nil {
			if selectSpan != nil {
				selectSpan.SetTag("error", "true")
				sentry.CaptureException(err)
			}
			selectCleanup()
			return fmt.Errorf("failed to query waiting calls: %w", err)
		}
		defer rows.Close()

		waitingCalls, err = scanQueuedCalls(rows)
		if err != nil {
			if selectSpan != nil {
				selectSpan.SetTag("error", "true")
				sentry.CaptureException(err)
			}
			selectCleanup()
			return fmt.Errorf("failed to scan waiting calls: %w", err)
		}
		if selectSpan != nil {
			selectSpan.SetTag("db.rows_affected", fmt.Sprintf("%d", len(waitingCalls)))
		}
		selectCleanup()

		return nil
	})

	if err != nil {
		if span != nil {
			span.SetTag("error", "true")
		}
		sentry.CaptureException(err)
		return 0, nil, err
	}

	if span != nil {
		span.SetTag("queue_size", fmt.Sprintf("%d", queueSize))
		span.SetTag("waiting_calls_count", fmt.Sprintf("%d", len(waitingCalls)))
	}
	return queueSize, waitingCalls, nil
}

// SetCallSituation assigns a situation to a call
func (r *postgresCallQueueRepository) SetCallSituation(ctx context.Context, callSID string, situationID string) error {
	return database.WithSessionErr(r.db, ctx, nil, func(tx *sql.Tx) error {
		if callSID == "" || situationID == "" {
			return ErrInvalidInput
		}

		result, err := tx.ExecContext(ctx, `
			UPDATE call_queue 
			SET situation_id = $1
			WHERE call_sid = $2
		`, situationID, callSID)
		if err != nil {
			return fmt.Errorf("failed to update situation ID: %w", err)
		}

		rows, err := result.RowsAffected()
		if err != nil {
			return fmt.Errorf("failed to get rows affected: %w", err)
		}
		if rows == 0 {
			return ErrCallNotFound
		}

		return nil
	})
}

// SetQueueStrategy sets the strategy used for selecting the next call
func (r *postgresCallQueueRepository) SetQueueStrategy(strategy QueueStrategy) {
	r.queueStrategy = strategy
}

// GetTwilioQueueSid returns the SID of the Twilio queue for the current organization.
// If no organization-specific queue is found, it falls back to the default queue.
func (r *postgresCallQueueRepository) GetTwilioQueueSid(ctx context.Context) (string, error) {
	return database.WithSession(r.db, ctx, nil, func(tx *sql.Tx) (string, error) {
		orgID := cmncontext.GetOrgId(ctx)

		log.Printf("Getting Twilio queue SID for org %d", orgID)

		var queueSid string
		err := tx.QueryRowContext(
			ctx,
			`SELECT twilio_queue_sid FROM twilio_queue_configurations WHERE org_id = $1`,
			orgID,
		).Scan(&queueSid)

		if err != nil {
			return "", fmt.Errorf("failed to get Twilio queue SID for org %d: %w", orgID, err)
		}

		return queueSid, nil
	})
}

// GetTwilioQueueName returns the friendly name of the Twilio queue for the current organization.
// If no organization-specific queue is found, it falls back to the default queue.
func (r *postgresCallQueueRepository) GetTwilioQueueName(ctx context.Context) (string, error) {
	return database.WithSession(r.db, ctx, nil, func(tx *sql.Tx) (string, error) {
		orgID := cmncontext.GetOrgId(ctx)

		log.Printf("Getting Twilio queue name for org %d", orgID)

		var queueName string
		err := tx.QueryRowContext(
			ctx,
			`SELECT friendly_name FROM twilio_queue_configurations WHERE org_id = $1`,
			orgID,
		).Scan(&queueName)

		if err != nil {
			return "", fmt.Errorf("failed to get Twilio queue name for org %d: %w", orgID, err)
		}

		return queueName, nil
	})
}

// DequeueCallBySid gets a specific call from the queue by SID and assigns it to an asset
func (r *postgresCallQueueRepository) DequeueCallBySid(ctx context.Context, callSID string, assetID string) (QueuedCall, bool, error) {
	span, cleanup := observability.StartSpanWithTransaction(ctx, "db.dequeue_call_by_sid", "CallQueue.DequeueCallBySid")
	defer cleanup()
	if span != nil {
		span.Op = "db.transaction"
		span.Description = "CallQueue.DequeueCallBySid"
		span.SetTag("db.operation", "SELECT_UPDATE")
		span.SetTag("db.table", "call_queue")
		span.SetTag("call_sid", callSID)
		span.SetTag("asset_id", assetID)
	}

	var call QueuedCall
	var found bool

	err := database.WithSessionErr(r.db, ctx, nil, func(tx *sql.Tx) error {
		if callSID == "" || assetID == "" {
			return ErrInvalidInput
		}

		// Get the specific call by SID, ensuring it's in waiting state
		selectSpan, selectCleanup := observability.StartSpanWithTransaction(ctx, "db.select_call_for_update", "CallQueue.SelectCallForUpdate")
		if selectSpan != nil {
			selectSpan.Op = dbQueryOperation
			selectSpan.SetTag("db.operation", "SELECT FOR UPDATE")
			selectSpan.SetTag("db.table", "call_queue")
			selectSpan.SetTag("call_sid", callSID)
		}
		row := tx.QueryRowContext(ctx, `
			SELECT call_sid, caller, caller_name, enqueue_time, 
				asset_id, situation_id, state, direction, attributes,
				call_start_time, call_end_time, last_hold_start
			FROM call_queue 
			WHERE call_sid = $1 AND state = $2
			FOR UPDATE
		`, callSID, CallStateWaiting)

		var scanErr error
		call, scanErr = scanQueuedCall(row)
		if selectSpan != nil {
			selectSpan.Description = "CallQueue.SelectCallForUpdate"
		}
		if scanErr == sql.ErrNoRows {
			if selectSpan != nil {
				selectSpan.SetTag("call_found", "false")
			}
			selectCleanup()
			return nil // Call not found or not in waiting state
		}
		if scanErr != nil {
			if selectSpan != nil {
				selectSpan.SetTag("error", "true")
			}
			sentry.CaptureException(scanErr)
			selectCleanup()
			return fmt.Errorf("failed to scan call: %w", scanErr)
		}
		if selectSpan != nil {
			selectSpan.SetTag("call_found", "true")
		}
		selectCleanup()
		found = true

		// Update the call to assign it to the asset and set to pending selective assignment state
		call.AssetID = assetID
		call.State = CallStatePendingSelectiveAssign

		attributesJSON, err := json.Marshal(call.Attributes)
		if err != nil {
			return fmt.Errorf("failed to marshal attributes: %w", err)
		}

		updateSpan, updateCleanup := observability.StartSpanWithTransaction(ctx, "db.update_call_assignment", "CallQueue.UpdateCallAssignment")
		if updateSpan != nil {
			updateSpan.Description = "CallQueue.UpdateCallAssignment"
			updateSpan.Op = dbQueryOperation
			updateSpan.SetTag("db.operation", "UPDATE")
			updateSpan.SetTag("db.table", "call_queue")
			updateSpan.SetTag("call_sid", callSID)
			updateSpan.SetTag("asset_id", assetID)
		}
		_, err = tx.ExecContext(ctx, `
			UPDATE call_queue 
			SET asset_id = $1, state = $2, attributes = $3
			WHERE call_sid = $4
		`, assetID, CallStatePendingSelectiveAssign, attributesJSON, callSID)
		if err != nil {
			if updateSpan != nil {
				updateSpan.SetTag("error", "true")
				updateSpan.SetTag("db.rows_affected", "0")
			}
			sentry.CaptureException(err)
			updateCleanup()
			return fmt.Errorf("failed to update call: %w", err)
		}
		if updateSpan != nil {
			updateSpan.SetTag("db.rows_affected", "1")
		}
		updateCleanup()

		// Re-fetch to get the updated call data
		refetchSpan, refetchCleanup := observability.StartSpanWithTransaction(ctx, "db.refetch_updated_call", "CallQueue.RefetchUpdatedCall")
		if refetchSpan != nil {
			refetchSpan.Description = "CallQueue.RefetchUpdatedCall"
			refetchSpan.Op = dbQueryOperation
			refetchSpan.SetTag("db.operation", "SELECT")
			refetchSpan.SetTag("db.table", "call_queue")
			refetchSpan.SetTag("call_sid", callSID)
		}
		rowAfterUpdate := tx.QueryRowContext(ctx, `
			SELECT call_sid, caller, caller_name, enqueue_time, 
				asset_id, situation_id, state, direction, attributes,
				call_start_time, call_end_time, last_hold_start
			FROM call_queue WHERE call_sid = $1`, callSID)
		updatedCall, scanErrAfterUpdate := scanQueuedCall(rowAfterUpdate)
		if scanErrAfterUpdate != nil {
			if refetchSpan != nil {
				refetchSpan.SetTag("error", "true")
			}
			sentry.CaptureException(scanErrAfterUpdate)
			refetchCleanup()
			return fmt.Errorf("failed to re-scan call after update: %w", scanErrAfterUpdate)
		}
		if refetchSpan != nil {
			refetchSpan.SetTag("db.rows_affected", "1")
		}
		refetchCleanup()
		call = updatedCall

		return nil
	})

	if err != nil {
		if span != nil {
			span.SetTag("error", "true")
		}
		sentry.CaptureException(err)
		return QueuedCall{}, false, err
	}

	if span != nil {
		span.SetTag("call_found", fmt.Sprintf("%t", found))
		if found {
			span.SetTag("final_state", call.State)
			span.SetTag("assigned_asset", call.AssetID)
		}
	}
	return call, found, nil
}

func (r *postgresCallQueueRepository) GetCallByCallSID(ctx context.Context, callSID string) (QueuedCall, bool, error) {
	var call QueuedCall
	var found bool

	err := database.WithSessionErr(r.db, ctx, nil, func(tx *sql.Tx) error {
		if callSID == "" {
			return ErrInvalidInput
		}

		row := tx.QueryRowContext(ctx, `
			SELECT call_sid, caller, caller_name, enqueue_time, 
				asset_id, situation_id, state, direction, attributes,
				call_start_time, call_end_time, last_hold_start
			FROM call_queue 
			WHERE call_sid = $1
		`, callSID)

		var err error
		call, err = scanQueuedCall(row)
		if err == sql.ErrNoRows {
			return nil
		}
		if err != nil {
			return fmt.Errorf("failed to scan call: %w", err)
		}
		found = true
		return nil
	})

	if err != nil {
		return QueuedCall{}, false, err
	}

	return call, found, nil
}

// StoreActiveCall stores or updates a call's details, respecting the incoming state.
// It performs an UPSERT operation: if the call_sid exists, it's updated; otherwise, it's inserted.
func (r *postgresCallQueueRepository) StoreActiveCall(ctx context.Context, call QueuedCall) error {
	return database.WithSessionErr(r.db, ctx, nil, func(tx *sql.Tx) error {
		// Validate requirements
		if call.CallSID == "" {
			log.Printf("StoreActiveCall: Missing CallSID, which is required for UPSERT.")
			return ErrInvalidInput // Or a more specific error like ErrCallSIDRequired
		}

		// For outbound calls, an AssetID (the agent initiating) is mandatory.
		if call.Direction == CallDirectionOutbound && call.AssetID == "" {
			log.Printf("StoreActiveCall: Missing AssetID for outbound call SID %s", call.CallSID)
			return ErrInvalidInput
		}

		// If a call is marked as active, it should generally be associated with an asset.
		if call.State == CallStateActive && call.AssetID == "" {
			log.Printf("StoreActiveCall: Missing AssetID for active call SID %s", call.CallSID)
			return ErrInvalidInput
		}

		orgID := cmncontext.GetOrgId(ctx)

		// Convert attributes to JSON
		attributesJSON, err := json.Marshal(call.Attributes)
		if err != nil {
			return fmt.Errorf("failed to marshal attributes: %w", err)
		}
		log.Printf("Postgres StoreActiveCall (UPSERTING): Storing/Updating call %s for asset %s with state %s. Attributes: %s", call.CallSID, call.AssetID, call.State, string(attributesJSON))

		// Handle potentially NULL fields for SQL arguments
		var callerNameArg sql.NullString
		if call.CallerName != "" {
			callerNameArg = sql.NullString{String: call.CallerName, Valid: true}
		} else {
			callerNameArg = sql.NullString{Valid: false}
		}

		var situationIDArg sql.NullString
		if call.SituationID != "" {
			situationIDArg = sql.NullString{String: call.SituationID, Valid: true}
		} else {
			situationIDArg = sql.NullString{Valid: false}
		}

		var assetIDArg sql.NullString
		if call.AssetID != "" {
			assetIDArg = sql.NullString{String: call.AssetID, Valid: true}
		} else {
			assetIDArg = sql.NullString{Valid: false}
		}

		// Ensure EnqueueTime is valid or null for database
		var enqueueTimeArg interface{}
		if call.EnqueueTime.IsZero() || call.EnqueueTime.Before(time.Date(1970, 1, 1, 0, 0, 0, 0, time.UTC)) {
			// For new inserts, if not provided, might default to NOW() or be explicitly NULL if appropriate
			// For updates, we'd typically COALESCE with existing value.
			// Given UPSERT, let's set to NULL if zero, DB default or COALESCE in query will handle it.
			enqueueTimeArg = nil
		} else {
			enqueueTimeArg = call.EnqueueTime
		}

		var callStartTimeArg interface{}
		if call.CallStartTime != nil && !call.CallStartTime.IsZero() {
			callStartTimeArg = *call.CallStartTime
		} else {
			// Pass NULL from Go if no specific start time is provided.
			// The SQL query will handle using NOW() for new inserts under appropriate conditions.
			callStartTimeArg = nil
		}

		var callEndTimeArg interface{}
		if call.CallEndTime != nil && !call.CallEndTime.IsZero() {
			callEndTimeArg = *call.CallEndTime
		} else {
			callEndTimeArg = nil
		}

		var lastHoldStartArg interface{}
		if call.LastHoldStart != nil && !call.LastHoldStart.IsZero() {
			lastHoldStartArg = *call.LastHoldStart
		} else {
			lastHoldStartArg = nil
		}

		// UPSERT query
		query := `
			INSERT INTO call_queue (
				call_sid, org_id, caller, caller_name, asset_id, 
				enqueue_time, situation_id, state, direction, attributes,
				call_start_time, call_end_time, last_hold_start
			) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, 
				COALESCE($11, CASE WHEN ($8 = 'active' OR ($9 = 'outbound' AND $8 != 'ended')) THEN NOW() ELSE NULL END),
				$12, $13)
			ON CONFLICT (call_sid) DO UPDATE SET
				org_id = EXCLUDED.org_id,
				caller = EXCLUDED.caller,
				caller_name = EXCLUDED.caller_name,
				asset_id = EXCLUDED.asset_id,
				enqueue_time = COALESCE(EXCLUDED.enqueue_time, call_queue.enqueue_time),
				situation_id = EXCLUDED.situation_id,
				state = EXCLUDED.state,
				direction = EXCLUDED.direction,
				attributes = EXCLUDED.attributes,
				call_start_time = COALESCE(call_queue.call_start_time, EXCLUDED.call_start_time),
				call_end_time = EXCLUDED.call_end_time,
				last_hold_start = CASE 
									WHEN EXCLUDED.state = $14 AND call_queue.state != $14 THEN NOW() 
									WHEN EXCLUDED.state != $14 THEN NULL
									ELSE call_queue.last_hold_start 
								  END
		`

		_, err = tx.ExecContext(ctx, query,
			call.CallSID,     // $1
			orgID,            // $2
			call.Caller,      // $3
			callerNameArg,    // $4
			assetIDArg,       // $5
			enqueueTimeArg,   // $6
			situationIDArg,   // $7
			call.State,       // $8
			call.Direction,   // $9
			attributesJSON,   // $10
			callStartTimeArg, // $11
			callEndTimeArg,   // $12
			lastHoldStartArg, // $13
			CallStateHold,    // $14 (for last_hold_start logic)
		)

		if err != nil {
			return fmt.Errorf("failed to upsert call record: %w", err)
		}

		return nil
	})
}
