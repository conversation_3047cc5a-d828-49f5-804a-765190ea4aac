package usecase

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"regexp"
	"strings"
	"time"

	clients "common/clients/services"
	"common/middleware"
	"common/utils"

	"github.com/getsentry/sentry-go"
	"github.com/twilio/twilio-go/client/jwt"
	"google.golang.org/protobuf/types/known/timestamppb"

	"communications/internal/cellularcall/client"
	"communications/internal/cellularcall/data"
	"communications/internal/cellularcall/twiml"

	assetsv2 "proto/hero/assets/v2"
	conversationv1 "proto/hero/communications/v1"
	orgs "proto/hero/orgs/v1"
	situationsv2 "proto/hero/situations/v2"

	"connectrpc.com/connect"
)

const (
	// Sentry span operation constants
	rpcServerOperation  = "rpc.server"
	httpServerOperation = "http.server"
)

// Twilio call status values (see: https://www.twilio.com/docs/voice/twiml/voice-status-callback)
const (
	TwilioCallStatusInitiated  = "initiated"
	TwilioCallStatusRinging    = "ringing"
	TwilioCallStatusInProgress = "in-progress"
	TwilioCallStatusCompleted  = "completed"
	TwilioCallStatusBusy       = "busy"
	TwilioCallStatusNoAnswer   = "no-answer"
	TwilioCallStatusFailed     = "failed"
	TwilioCallStatusCanceled   = "canceled"
)

// CellularCallUsecase defines the business logic for Twilio-based cellular calls.
type CellularCallUsecase interface {
	// GetCellularCallAccessToken generates a Twilio Access Token for cellular calls.
	GetCellularCallAccessToken(ctx context.Context, req *conversationv1.GetCellularCallAccessTokenRequest) (*conversationv1.GetCellularCallAccessTokenResponse, error)
	// HandleCall builds a TwiML response for handling inbound or outbound calls.
	HandleCall(ctx context.Context, req *conversationv1.HandleCallRequest) (*conversationv1.HandleCallResponse, error)
	// Queue management methods
	QueueCall(ctx context.Context, req *conversationv1.QueueCallRequest) (*conversationv1.QueueCallResponse, error)
	DequeueCall(ctx context.Context, req *conversationv1.DequeueCallRequest) (*conversationv1.DequeueCallResponse, error)
	DequeueCallBySid(ctx context.Context, req *conversationv1.DequeueCallBySidRequest) (*conversationv1.DequeueCallBySidResponse, error)
	GetQueueStatus(ctx context.Context, req *conversationv1.GetQueueStatusRequest) (*conversationv1.GetQueueStatusResponse, error)
	HoldCall(ctx context.Context, req *conversationv1.HoldCallRequest) (*conversationv1.HoldCallResponse, error)
	ResumeCall(ctx context.Context, req *conversationv1.ResumeCallRequest) (*conversationv1.ResumeCallResponse, error)
	GetAssetHeldCalls(ctx context.Context, req *conversationv1.GetAssetHeldCallsRequest) (*conversationv1.GetAssetHeldCallsResponse, error)
	EndCall(ctx context.Context, req *conversationv1.EndCallRequest) (*conversationv1.EndCallResponse, error)
	// VoiceHandler is the unified HTTP endpoint for inbound calls
	HandleVoiceRequest(w http.ResponseWriter, r *http.Request)
	// HandleCallStatusRequest is the unified HTTP endpoint for call status updates
	HandleCallStatusRequest(w http.ResponseWriter, r *http.Request)
	// HandleAgentDialStatusRequest processes Twilio status callbacks for the agent leg of calls
	HandleAgentDialStatusRequest(w http.ResponseWriter, r *http.Request)
	// HandleConnectAgentTwiMLRequest generates TwiML to connect an agent to a specific customer call
	HandleConnectAgentTwiMLRequest(w http.ResponseWriter, r *http.Request)
	// HandleWaitHoldRequest generates smart TwiML for queue wait and hold messaging
	HandleWaitHoldRequest(w http.ResponseWriter, r *http.Request)
	GetSituationForCall(ctx context.Context, req *conversationv1.GetSituationForCallRequest) (*conversationv1.GetSituationForCallResponse, error)
	// RevertSelectiveClaim reverts a call from pending_selective_assignment back to waiting state
	RevertSelectiveClaim(ctx context.Context, req *conversationv1.RevertSelectiveClaimRequest) (*conversationv1.RevertSelectiveClaimResponse, error)
}

// FIFOStrategy implements a first-in, first-out queue selection strategy
type FIFOStrategy struct{}

// SelectNextCall selects the first call in the waiting queue
func (s *FIFOStrategy) SelectNextCall(ctx context.Context, calls []data.QueuedCall) (data.QueuedCall, int, bool, error) {
	if len(calls) == 0 {
		return data.QueuedCall{}, -1, false, nil
	}
	return calls[0], 0, true, nil
}

type cellularCallUsecase struct {
	twilioAccountSid        string
	twilioAPIKeySid         string
	twilioAPIKeySecret      string
	twilioAuthToken         string
	callQueueRepo           data.CallQueueRepository
	waitURL                 string
	situationClient         clients.SituationsClient
	twilioQueueName         string
	orgClient               clients.OrgsClient
	twilioClient            client.TwilioClient
	commsServerPublicDomain string
	assetsClient            clients.AssetsClient
}

// NewCellularCallUsecase creates a new CellularCallUsecase with the required Twilio credentials.
func NewCellularCallUsecase(
	twilioAccountSid string,
	twilioAPIKeySid string,
	twilioAPIKeySecret string,
	twilioAuthToken string,
	waitURL string,
	situationsServiceURL string,
	orgsServiceURL string,
	commsServerPublicDomain string,
	assetsServiceURL string,
) (CellularCallUsecase, error) {
	// Validate required parameters.
	if twilioAccountSid == "" || twilioAPIKeySid == "" || twilioAPIKeySecret == "" {
		return nil, errors.New("missing required Twilio credentials")
	}

	// Create the call queue repository using the fetched SID.
	queueRepo, err := data.NewCallQueueRepository() // Use actual SID here
	if err != nil {
		return nil, fmt.Errorf("failed to create call queue repository: %w", err)
	}

	// Set default FIFO strategy for the queue
	queueRepo.SetQueueStrategy(&FIFOStrategy{})

	// Create tracing interceptor for distributed tracing across services
	tracingInterceptor := middleware.SentryTracingInterceptor("communications-service")

	// Initialize situation client with distributed tracing
	situationClient := clients.NewSituationsClient(situationsServiceURL, tracingInterceptor)

	// Initialize org client with distributed tracing
	orgClient := clients.NewOrgsClient(orgsServiceURL, tracingInterceptor)

	// Initialize assets client with distributed tracing
	assetsClient := clients.NewAssetsClient(assetsServiceURL, tracingInterceptor)

	// Create the Twilio client
	twilioClient := client.NewTwilioClient(twilioAccountSid, twilioAuthToken)

	return &cellularCallUsecase{
		twilioAccountSid:        twilioAccountSid,
		twilioAPIKeySid:         twilioAPIKeySid,
		twilioAPIKeySecret:      twilioAPIKeySecret,
		twilioAuthToken:         twilioAuthToken,
		callQueueRepo:           queueRepo,
		waitURL:                 waitURL,
		situationClient:         situationClient,
		orgClient:               orgClient,
		twilioClient:            twilioClient,
		commsServerPublicDomain: commsServerPublicDomain,
		assetsClient:            assetsClient,
	}, nil
}

// sanitizeTwilioIdentity removes invalid characters from Twilio identities
// Twilio identities must be alphanumeric, hyphens, underscores, and periods only
func sanitizeTwilioIdentity(identity string) string {
	// Replace spaces with hyphens
	identity = strings.ReplaceAll(identity, " ", "-")

	// Remove any characters that aren't alphanumeric, hyphens, underscores, or periods
	reg := regexp.MustCompile(`[^a-zA-Z0-9\-_.]`)
	identity = reg.ReplaceAllString(identity, "")

	// Ensure it's not empty
	if identity == "" {
		identity = "unknown"
	}

	return identity
}

// GetCellularCallAccessToken generates a Twilio Access Token with a VoiceGrant.
func (uc *cellularCallUsecase) GetCellularCallAccessToken(
	ctx context.Context,
	req *conversationv1.GetCellularCallAccessTokenRequest,
) (*conversationv1.GetCellularCallAccessTokenResponse, error) {
	transaction := middleware.StartTransactionWithTraceContext(ctx, "CellularCall/GetCellularCallAccessToken")
	defer transaction.Finish()
	ctx = transaction.Context()
	span := transaction
	span.Op = rpcServerOperation
	span.Description = "CellularCall.GetCellularCallAccessToken"
	span.SetTag("rpc.service", "CellularCall")
	span.SetTag("rpc.method", "GetCellularCallAccessToken")
	span.SetTag("service", "communications-service")
	span.SetTag("identity", req.Identity)
	span.SetTag("session_suffix", req.SessionSuffix)
	span.SetTag("expire", fmt.Sprintf("%d", req.Expire))

	// Simple validation - asset ID is required for token generation
	if req.Identity == "" {
		err := fmt.Errorf("asset ID is required for token generation")
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		return nil, err
	}

	orgTwilioDetails, err := uc.callQueueRepo.GetOrgTwilioDetails(ctx)
	if err != nil {
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		return nil, fmt.Errorf("failed to get org twilio details: %w", err)
	}

	// Build identity using asset name + session suffix, falling back to asset ID if needed
	var identity string
	var idPrefixPart string
	var namePartVal string
	const prefixLength = 8 // Define how many characters for the prefix

	// Use assetID for prefix part
	if len(req.Identity) > prefixLength {
		idPrefixPart = req.Identity[:prefixLength]
	} else {
		idPrefixPart = req.Identity // Use full assetID if shorter than prefixLength
	}

	// Try to get asset name for the name part
	assetLookupSpan := sentry.StartSpan(ctx, "communications.lookup_asset")
	assetLookupSpan.Description = "Communications.LookupAsset"
	assetLookupSpan.SetTag("operation", "lookup_asset")
	assetLookupSpan.SetTag("asset_id", req.Identity)
	getAssetReq := &assetsv2.GetAssetRequest{Id: req.Identity}
	assetResp, err := uc.assetsClient.GetAsset(assetLookupSpan.Context(), connect.NewRequest(getAssetReq))
	assetLookupSpan.Finish()

	var fetchedAssetName string
	if err != nil {
		assetLookupSpan.SetTag("error", "true")
		assetLookupSpan.SetTag("fallback_used", "true")
		sentry.CaptureException(err)
		log.Printf("Warning: Failed to fetch asset name for %s, using asset ID as fallback for name part: %v", req.Identity, err)
		fetchedAssetName = req.Identity // Use asset ID as fallback for name part
	} else {
		assetLookupSpan.SetTag("asset_name", assetResp.Msg.Asset.Name)
		fetchedAssetName = assetResp.Msg.Asset.Name
		if fetchedAssetName == "" {
			assetLookupSpan.SetTag("fallback_used", "true")
			log.Printf("Warning: Asset %s has empty name, using asset ID as fallback for name part", req.Identity)
			fetchedAssetName = req.Identity // Use asset ID as fallback for name part
		}
	}

	// Sanitize the fetched asset name (which could be the assetID fallback) for Twilio
	namePartVal = sanitizeTwilioIdentity(fetchedAssetName)

	// Construct the identity string in the new format: idPrefixPart-namePartVal-sessionSuffix
	if req.SessionSuffix != "" {
		identity = fmt.Sprintf("%s-%s-%s", idPrefixPart, namePartVal, req.SessionSuffix)
	} else {
		// If no sessionSuffix, omit it and its preceding separator from the format
		identity = fmt.Sprintf("%s-%s", idPrefixPart, namePartVal)
	}

	expireSeconds := req.Expire
	if expireSeconds <= 0 {
		expireSeconds = 3600
	}

	params := jwt.AccessTokenParams{
		AccountSid:    uc.twilioAccountSid,
		SigningKeySid: uc.twilioAPIKeySid,
		Secret:        uc.twilioAPIKeySecret,
		Identity:      identity,
		Ttl:           float64(expireSeconds),
	}

	tokenObj := jwt.CreateAccessToken(params)

	voiceGrant := &jwt.VoiceGrant{
		Incoming: jwt.Incoming{
			Allow: true,
		},
		Outgoing: jwt.Outgoing{
			ApplicationSid: orgTwilioDetails.TwimlAppSid,
		},
	}
	tokenObj.AddGrant(voiceGrant)

	// Convert to JWT string
	tokenString, err := tokenObj.ToJwt()
	if err != nil {
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		return nil, fmt.Errorf("failed to generate Twilio access token: %w", err)
	}

	// Log successful token generation
	log.Printf("Generated Twilio access token for identity: %s (expires in %d seconds)",
		identity, expireSeconds)

	return &conversationv1.GetCellularCallAccessTokenResponse{
		Token:    tokenString,
		Identity: identity,
	}, nil
}

// QueueCall enqueues a call and generates TwiML to place the caller in a queue.
func (uc *cellularCallUsecase) QueueCall(
	ctx context.Context,
	req *conversationv1.QueueCallRequest,
) (*conversationv1.QueueCallResponse, error) {
	// Use helper function to continue distributed traces if available
	transaction := middleware.StartTransactionWithTraceContext(ctx, "CellularCall/QueueCall")
	defer transaction.Finish()
	ctx = transaction.Context()
	span := transaction
	span.Op = rpcServerOperation
	span.Description = "CellularCall.QueueCall"
	span.SetTag("rpc.service", "CellularCall")
	span.SetTag("rpc.method", "QueueCall")
	span.SetTag("service", "communications-service")
	span.SetTag("caller", req.Caller)
	span.SetTag("caller_name", req.CallerName)

	log.Printf("QueueCall: Starting to enqueue call from %s", req.Caller)

	if req.Caller == "" {
		err := errors.New("caller number is required")
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		log.Printf("ERROR: Caller number is missing in request")
		return nil, err
	}

	callSID := ""
	if req.Attributes != nil && req.Attributes["CallSid"] != "" {
		callSID = req.Attributes["CallSid"]
		log.Printf("Using Twilio CallSID: %s", callSID)
	} else {
		callSID = fmt.Sprintf("queue-call-%d", time.Now().Unix())
		log.Printf("Generated internal CallSID: %s", callSID)
	}
	span.SetTag("call_sid", callSID)

	// Create the queued call
	log.Printf("Creating QueuedCall object with caller: %s, name: %s", req.Caller, req.CallerName)
	queuedCall := data.QueuedCall{
		CallSID:     callSID,
		Caller:      req.Caller,
		CallerName:  req.CallerName,
		EnqueueTime: time.Now(),
		State:       data.CallStateWaiting,
		Attributes:  make(map[string]string),
	}

	if req.Attributes != nil {
		log.Printf("Adding %d attributes to call", len(req.Attributes))
		for k, v := range req.Attributes {
			queuedCall.Attributes[k] = v
		}
	}

	// Create situation
	log.Printf("Attempting to create default situation for call")
	situationSpan := sentry.StartSpan(ctx, "communications.create_default_situation")
	situationSpan.Description = "Communications.CreateDefaultSituation"
	situationSpan.SetTag("operation", "create_default_situation")
	situationSpan.SetTag("caller", queuedCall.Caller)
	situationSpan.SetTag("call_sid", callSID)
	situationID, err := uc.createDefaultSituationForCall(situationSpan.Context(), queuedCall)
	situationSpan.Finish()
	if err != nil {
		situationSpan.SetTag("error", "true")
		sentry.CaptureException(err)
		log.Printf("Warning: Failed to create situation for call %s: %v", callSID, err)
	} else {
		situationSpan.SetTag("situation_id", situationID)
		queuedCall.SituationID = situationID
		log.Printf("Created situation %s for call %s", situationID, callSID)
	}

	// Add to queue repository
	log.Printf("Adding call to queue repository")
	err = uc.callQueueRepo.EnqueueCall(ctx, queuedCall)
	if err != nil {
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		log.Printf("ERROR: Failed to enqueue call in repository: %v", err)
		return nil, fmt.Errorf("failed to enqueue call: %w", err)
	}
	log.Printf("Successfully added call to queue repository")

	// Get the queue name for the current organization
	queueName, err := uc.callQueueRepo.GetTwilioQueueName(ctx)
	if err != nil {
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		return nil, fmt.Errorf("error getting org-specific queue name: %v", err)
	}

	queueSid, err := uc.callQueueRepo.GetTwilioQueueSid(ctx)
	if err != nil {
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		return nil, fmt.Errorf("error getting org-specific queue SID: %v", err)
	}

	// Get org Twilio details for authentication
	orgTwilioDetails, err := uc.callQueueRepo.GetOrgTwilioDetails(ctx)
	if err != nil {
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		return nil, fmt.Errorf("failed to get org twilio details: %w", err)
	}

	// Get org API user credentials for basic auth
	orgUserLookupSpan := sentry.StartSpan(ctx, "communications.lookup_org_api_user")
	orgUserLookupSpan.Description = "Communications.LookupOrgAPIUser"
	orgUserLookupSpan.SetTag("operation", "lookup_org_api_user")
	orgUserLookupSpan.SetTag("user_id", orgTwilioDetails.TwilioApiUserId)
	getOrgAPIUserPrivateRequest := &orgs.GetOrgAPIUserPrivateByIdRequest{
		UserId: orgTwilioDetails.TwilioApiUserId,
	}
	basicTwilioUserAuth, err := uc.orgClient.GetOrgAPIUserPrivateById(orgUserLookupSpan.Context(), connect.NewRequest(getOrgAPIUserPrivateRequest))
	orgUserLookupSpan.Finish()
	if err != nil {
		orgUserLookupSpan.SetTag("error", "true")
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		log.Printf("ERROR: Failed to get org API user for smart wait URL: %v", err)
		return nil, fmt.Errorf("failed to get org API user for authentication: %w", err)
	} else {
		orgUserLookupSpan.SetTag("username", basicTwilioUserAuth.Msg.OrgApiUser.Username)
	}

	// Generate TwiML with smart wait endpoint including basic auth
	twimlGenerationSpan := sentry.StartSpan(ctx, "communications.generate_queue_twiml")
	twimlGenerationSpan.Description = "Communications.GenerateQueueTwiML"
	twimlGenerationSpan.SetTag("operation", "generate_queue_twiml")
	twimlGenerationSpan.SetTag("queue_name", queueName)
	twimlGenerationSpan.SetTag("call_sid", callSID)
	basicUserAuth := basicTwilioUserAuth.Msg.OrgApiUser.Username + ":" + basicTwilioUserAuth.Msg.OrgApiUser.RawPassword
	smartWaitURL := fmt.Sprintf("https://%s@%s/hero.communications.v1.TwilioWebhookService/waithold?action=enqueue", basicUserAuth, uc.commsServerPublicDomain)
	log.Printf("Generating TwiML with queue name: %s, smartWaitUrl: %s", queueName, smartWaitURL)
	// Remove welcome message - let smart wait messaging handle all caller communication
	twimlResponse, err := twiml.QueueResponse(queueName, smartWaitURL, "")
	twimlGenerationSpan.Finish()
	if err != nil {
		twimlGenerationSpan.SetTag("error", "true")
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		log.Printf("ERROR: Failed to generate TwiML: %v", err)
		return nil, fmt.Errorf("failed to generate queue TwiML: %w", err)
	} else {
		twimlGenerationSpan.SetTag("twiml_length", fmt.Sprintf("%d", len(twimlResponse)))
	}

	log.Printf("Successfully generated TwiML: %s", twimlResponse)

	response := &conversationv1.QueueCallResponse{
		QueueName: queueName,
		QueueSid:  queueSid,
		CallSid:   callSID,
		Twiml:     twimlResponse,
	}
	span.SetTag("rpc.status", "success")
	log.Printf("QueueCall: Completed successfully, returning response")
	return response, nil
}

// DequeueCall connects an asset to the next call in the queue.
// DEPRECATED: This method is obsolete. Use DequeueCallBySid for selective call handling.

func (uc *cellularCallUsecase) DequeueCall(
	ctx context.Context,
	req *conversationv1.DequeueCallRequest,
) (*conversationv1.DequeueCallResponse, error) {
	log.Printf("DequeueCall: Asset %s accepting next call", req.AssetId)
	if req.AssetId == "" {
		return nil, errors.New("asset ID is required")
	}

	// Try to dequeue a call from the repository
	call, ok, err := uc.callQueueRepo.DequeueCall(ctx, req.AssetId)
	if err != nil {
		return nil, fmt.Errorf("failed to dequeue call: %w", err)
	}

	if !ok {
		log.Print("No calls available in queue")
		return &conversationv1.DequeueCallResponse{
			Success:    false,
			CallSid:    "",
			Caller:     "",
			CallerName: "",
		}, nil
	}

	// Convert repository attributes to proto message attributes
	attributes := make(map[string]string)
	for k, v := range call.Attributes {
		attributes[k] = v
	}

	if call.SituationID != "" {
		log.Printf("Call dequeued with situation ID: %s", call.SituationID)
	}

	log.Printf("Dequeued call %s from %s for asset %s", call.CallSID, call.Caller, req.AssetId)

	// Create the response
	return &conversationv1.DequeueCallResponse{
		Success:       true,
		CallSid:       call.CallSID,
		Caller:        call.Caller,
		CallerName:    call.CallerName,
		Attributes:    attributes,
		QueueName:     uc.twilioQueueName,
		SituationId:   call.SituationID,
		CallStartTime: timestamppb.New(*call.CallStartTime),
	}, nil
}

// DequeueCallBySid connects an asset to a specific call by SID
func (uc *cellularCallUsecase) DequeueCallBySid(
	ctx context.Context,
	req *conversationv1.DequeueCallBySidRequest,
) (*conversationv1.DequeueCallBySidResponse, error) {
	transaction := middleware.StartTransactionWithTraceContext(ctx, "CellularCall/DequeueCallBySid")
	defer transaction.Finish()
	ctx = transaction.Context()
	span := transaction
	span.Op = rpcServerOperation
	span.Description = "CellularCall.DequeueCallBySid"
	span.SetTag("rpc.service", "CellularCall")
	span.SetTag("rpc.method", "DequeueCallBySid")
	span.SetTag("service", "communications-service")
	span.SetTag("call_sid", req.CallSid)
	span.SetTag("asset_id", req.AssetId)
	span.SetTag("session_suffix", req.SessionSuffix)

	log.Printf("DequeueCallBySid: Starting to dequeue call %s for asset %s", req.CallSid, req.AssetId)

	if req.AssetId == "" || req.CallSid == "" {
		err := errors.New("missing required parameters: asset_id and call_sid")
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		return nil, err
	}

	// Try to dequeue the specific call by SID
	call, found, err := uc.callQueueRepo.DequeueCallBySid(ctx, req.CallSid, req.AssetId)
	if err != nil {
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		log.Printf("Error dequeuing call by SID from repository: %v", err)
		return nil, fmt.Errorf("failed to dequeue call from repository: %w", err)
	}

	if !found {
		span.SetTag("call_not_found", "true")
		log.Printf("Call with SID %s not found or not in waiting state in repository", req.CallSid)
		return &conversationv1.DequeueCallBySidResponse{
			Success: false,
		}, nil
	}

	// Construct the relative path for the TwiML endpoint
	relativeTwiMLPath := fmt.Sprintf("/hero.communications.v1.TwilioWebhookService/twiml/connectAgent?agentId=%s&customerSid=%s&sessionSuffix=%s",
		req.AssetId, call.CallSID, req.SessionSuffix)

	// Env var if local development. Read the readme for more details. (COMMS_SERVER_PUBLIC_DOMAIN)
	publicCommunicationsServiceURL := "https://" + uc.commsServerPublicDomain

	connectAgentURL := publicCommunicationsServiceURL + relativeTwiMLPath
	log.Printf("DequeueCallBySid: Constructed connectAgentURL for redirection: %s", connectAgentURL)

	var redirectErr error
	maxRetries := 4 // Number of retries (total 5 attempts)
	retryDelay := 500 * time.Millisecond

	// Get the queue SID for the current organization
	queueSid, err := uc.callQueueRepo.GetTwilioQueueSid(ctx)
	if err != nil {
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		return nil, fmt.Errorf("failed to get org-specific queue SID: %w", err)
	}

	for i := 0; i <= maxRetries; i++ {
		log.Printf("Attempt %d to redirect queue member %s (Queue: %s) to %s", i+1, call.CallSID, queueSid, connectAgentURL)
		redirectErr = uc.twilioClient.RedirectQueueMember(ctx, queueSid, call.CallSID, connectAgentURL)
		if redirectErr == nil {
			// Success
			log.Printf("Successfully redirected queue member %s on attempt %d", call.CallSID, i+1)
			break
		}

		log.Printf("Attempt %d failed to redirect queue member %s: %v", i+1, call.CallSID, redirectErr)

		if i < maxRetries {
			log.Printf("Waiting %v before next retry...", retryDelay)
			time.Sleep(retryDelay)
			retryDelay *= 2 // Exponential backoff (e.g., 500ms, 1s)
		}
	}

	if redirectErr != nil {
		span.SetTag("error", "true")
		span.SetTag("redirect_failed", "true")
		span.SetTag("retry_count", fmt.Sprintf("%d", maxRetries+1))
		sentry.CaptureException(redirectErr)
		log.Printf("All %d attempts failed to redirect queue member %s. Last error: %v", maxRetries+1, call.CallSID, redirectErr)

		// Revert the call state in the database
		log.Printf("Reverting call %s state in DB due to redirection failure.", call.CallSID)
		// Revert the state *before* returning the error to the client.
		// Use a new context for this critical cleanup operation if the original context might have been cancelled.
		revertCtx, cancelRevert := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancelRevert()

		reverted, dbRevertErr := uc.callQueueRepo.RevertSelectiveClaim(revertCtx, call.CallSID)
		switch {
		case dbRevertErr != nil:
			sentry.CaptureException(dbRevertErr)
			log.Printf("CRITICAL: Failed to revert call state for %s after redirection failure: %v. Original error: %v", call.CallSID, dbRevertErr, redirectErr)
			// Even if revert fails, we should still return the original redirection error,
			// as that's what the caller (frontend) needs to know about.
		case reverted:
			log.Printf("Successfully reverted call %s to waiting state after redirection failure.", call.CallSID)
		default:
			log.Printf("No need to revert call %s state after redirection failure.", call.CallSID)
		}
		// Return the original error from the Twilio client
		return nil, fmt.Errorf("failed to redirect queue member after %d attempts: %w", maxRetries+1, redirectErr)
	}

	// Create response
	span.SetTag("rpc.status", "success")
	resp := &conversationv1.DequeueCallBySidResponse{
		Success:     true,
		CallSid:     call.CallSID,
		Caller:      call.Caller,
		CallerName:  call.CallerName,
		SituationId: call.SituationID,
	}

	log.Printf("DequeueCallBySid: Successfully claimed call %s for asset %s", call.CallSID, req.AssetId)
	return resp, nil
}

// createDefaultSituationForCall creates a basic situation for an incoming call.
func (uc *cellularCallUsecase) createDefaultSituationForCall(ctx context.Context, call data.QueuedCall) (string, error) {
	// First, check if there's an existing active situation for this caller
	searchReq := &situationsv2.SearchSituationsRequest{
		Query: call.Caller, // Search by caller number in all searchable fields
		Status: []situationsv2.SituationStatus{
			situationsv2.SituationStatus_SITUATION_STATUS_CREATED,
			situationsv2.SituationStatus_SITUATION_STATUS_TRIAGING,
			situationsv2.SituationStatus_SITUATION_STATUS_DISPATCHING,
			situationsv2.SituationStatus_SITUATION_STATUS_ADDRESSING,
			situationsv2.SituationStatus_SITUATION_STATUS_ESCALATED,
		},
		PageSize: 1,
		OrderBy:  situationsv2.SearchOrderBy_SEARCH_ORDER_BY_UPDATE_TIME, // Get most recently updated
	}

	searchResp, err := uc.situationClient.SearchSituations(ctx, connect.NewRequest(searchReq))
	if err != nil {
		return "", fmt.Errorf("error searching for existing situations: %w", err)
	}

	// If we found an active situation, return its ID
	if len(searchResp.Msg.Situations) > 0 {
		// Verify the situation is actually for this caller (since we're using a general search)
		situation := searchResp.Msg.Situations[0]
		if situation.ContactNo == call.Caller {
			return situation.Id, nil
		}
	}

	// If we get here, either no active situation was found or the found one wasn't for this caller
	// In either case, we'll proceed to create a new situation

	details := map[string]interface{}{
		"caller":      call.Caller,
		"callerName":  call.CallerName,
		"callTime":    time.Now().Format(time.RFC3339),
		"description": fmt.Sprintf("Incoming call from %s (%s)", call.CallerName, call.Caller),
		"callerNo":    call.Caller,
	}

	currentTime := time.Now()
	updates := []*situationsv2.UpdateEntry{
		{
			Message:      fmt.Sprintf("Incident is created by phonecall coming from %s", call.Caller),
			Timestamp:    utils.TimeToISO8601String(currentTime),
			UpdateSource: situationsv2.UpdateSource_UPDATE_SOURCE_API_SIDE_EFFECT,
			EventType:    "info change",
		},
	}

	detailsJSON, err := json.Marshal(details)
	if err != nil {
		return "", fmt.Errorf("failed to marshal incident details: %w", err)
	}

	situation := &situationsv2.Situation{
		Title:              fmt.Sprintf("Incoming call from %s", call.Caller),
		Type:               situationsv2.SituationType_SITUATION_TYPE_UNSPECIFIED,
		AdditionalInfoJson: string(detailsJSON),
		Status:             situationsv2.SituationStatus_SITUATION_STATUS_CREATED,
		TriggerSource:      situationsv2.TriggerSource_TRIGGER_SOURCE_PHONE_CALL,
		ContactNo:          call.Caller,
		CreateTime:         utils.TimeToISO8601String(currentTime),
		UpdateTime:         utils.TimeToISO8601String(currentTime),
		Updates:            updates,
	}

	createReq := &situationsv2.CreateSituationRequest{
		Situation: situation,
	}

	createResp, err := uc.situationClient.CreateSituation(ctx, connect.NewRequest(createReq))
	if err != nil {
		return "", fmt.Errorf("failed to create situation: %w", err)
	}

	return createResp.Msg.Situation.Id, nil
}

func (uc *cellularCallUsecase) GetSituationForCall(
	ctx context.Context,
	req *conversationv1.GetSituationForCallRequest,
) (*conversationv1.GetSituationForCallResponse, error) {
	// Add debug logs here
	log.Printf("GetSituationForCall called for CallSID: %s", req.CallSid)

	// Get call details from our repository
	call, found, err := uc.callQueueRepo.GetCallByCallSID(ctx, req.CallSid)
	if err != nil {
		log.Printf("Error getting call: %v", err)
		return nil, fmt.Errorf("error retrieving call: %w", err)
	}
	if !found {
		log.Printf("Call not found: %s", req.CallSid)
		return &conversationv1.GetSituationForCallResponse{
			Found: false,
		}, nil
	}

	log.Printf("Call found with situation ID: %s", call.SituationID)

	// Check if the call has a linked situation
	if call.SituationID == "" {
		return &conversationv1.GetSituationForCallResponse{
			Found: false,
		}, nil
	}

	getReq := &situationsv2.GetSituationRequest{
		Id: call.SituationID,
	}

	// Retrieve the situation details
	situationResp, err := uc.situationClient.GetSituation(ctx,
		connect.NewRequest(getReq))
	if err != nil {
		log.Printf("Error getting situation: %v", err)
		return nil, fmt.Errorf("error retrieving situation: %w", err)
	}

	situation := situationResp.Msg

	log.Printf("Retrieved situation: %+v", situation)

	return &conversationv1.GetSituationForCallResponse{
		Found:       true,
		SituationId: call.SituationID,
		Situation:   situation,
	}, nil
}

// Helper function to safely convert an int to int32 with overflow checking
func safeInt32Conversion(value int) int32 {
	if value > int(^uint32(0)>>1) {
		return int32(^uint32(0) >> 1) // Max int32 value
	}
	return int32(value) //nolint:gosec // Safe conversion
}

// GetQueueStatus returns the current state of the queue.
func (uc *cellularCallUsecase) GetQueueStatus(
	ctx context.Context,
	req *conversationv1.GetQueueStatusRequest,
) (*conversationv1.GetQueueStatusResponse, error) {
	queueSize, waitingCalls, err := uc.callQueueRepo.GetQueueStatus(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get queue status: %w", err)
	}
	var holdSize int
	allAssets := make(map[string]bool)
	for i := range waitingCalls {
		call := waitingCalls[i]
		if call.AssetID != "" {
			allAssets[call.AssetID] = true
		}
	}
	for assetID := range allAssets {
		heldCalls, err := uc.callQueueRepo.GetHeldCalls(ctx, assetID)
		if err != nil {
			log.Printf("Warning: Failed to get held calls for asset %s: %v", assetID, err)
			continue
		}
		holdSize += len(heldCalls)
	}
	protoWaitingCalls := make([]*conversationv1.QueuedCall, len(waitingCalls))
	for i := range waitingCalls {
		// Use the helper function for mapping
		protoWaitingCalls[i] = mapRepoCallToProtoCall(&waitingCalls[i])
	}

	var nextCall *conversationv1.QueuedCall
	if queueSize > 0 && len(protoWaitingCalls) > 0 {
		nextCall = protoWaitingCalls[0] // Assuming the first waiting call is the next one (FIFO)
	}

	safeQueueSize := safeInt32Conversion(queueSize)
	safeHoldSize := safeInt32Conversion(holdSize)

	return &conversationv1.GetQueueStatusResponse{
		QueueSize:    safeQueueSize,
		HoldSize:     safeHoldSize,
		NextCall:     nextCall,          // Already mapped by the loop above if it exists
		WaitingCalls: protoWaitingCalls, // Already mapped by the loop above
		// OnHoldCalls field is not populated here, GetAssetHeldCalls handles that.
	}, nil
}

// HoldCall places a call on hold.
func (uc *cellularCallUsecase) HoldCall(
	ctx context.Context,
	req *conversationv1.HoldCallRequest,
) (*conversationv1.HoldCallResponse, error) {
	transaction := middleware.StartTransactionWithTraceContext(ctx, "CellularCall/HoldCall")
	defer transaction.Finish()
	ctx = transaction.Context()
	span := transaction
	span.Op = rpcServerOperation
	span.Description = "CellularCall.HoldCall"
	span.SetTag("rpc.service", "CellularCall")
	span.SetTag("rpc.method", "HoldCall")
	span.SetTag("service", "communications-service")
	span.SetTag("call_sid", req.CallSid)
	span.SetTag("asset_id", req.AssetId)

	log.Printf("HoldCall: Placing call %s on hold for asset %s", req.CallSid, req.AssetId)

	// 1. Update internal state
	err := uc.callQueueRepo.HoldCall(ctx, req.CallSid, req.AssetId)
	if err != nil {
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		return nil, fmt.Errorf("failed to hold call in repo: %w", err)
	}

	// Logging to check direction of call
	call, found, err := uc.callQueueRepo.GetCallByCallSID(ctx, req.CallSid)
	if err != nil {
		log.Printf("Error retrieving call %s: %v", req.CallSid, err)
	} else if found {
		log.Printf("Holding call %s - Direction: %s, State: %s", req.CallSid, call.Direction, call.State)
	}

	// 2. Send hold music via Twilio REST API using direct TwiML
	holdTwiML, err := twiml.HoldResponse("Thank you for your patience. Your safety matters to us, and our team is actively working to assist you. Please stay on the line—someone will be with you shortly.")
	if err != nil {
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		return nil, fmt.Errorf("failed to generate hold TwiML: %w", err)
	}

	// Log the TwiML we're sending
	log.Printf("Sending hold TwiML to modify call: %s", holdTwiML)

	if err := uc.twilioClient.ModifyCall(ctx, req.CallSid, holdTwiML); err != nil {
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		return nil, fmt.Errorf("failed to place call on hold: %w", err)
	}

	span.SetTag("rpc.status", "success")
	return &conversationv1.HoldCallResponse{Success: true}, nil
}

// Helper function to map repository QueuedCall to Protobuf QueuedCall
func mapRepoCallToProtoCall(call *data.QueuedCall) *conversationv1.QueuedCall {
	if call == nil {
		return nil
	}

	attributes := make(map[string]string)
	for k, v := range call.Attributes {
		attributes[k] = v
	}

	protoCall := &conversationv1.QueuedCall{
		CallSid:     call.CallSID,
		Caller:      call.Caller,
		CallerName:  call.CallerName,
		EnqueueTime: timestamppb.New(call.EnqueueTime),
		Attributes:  attributes,
		AssetId:     call.AssetID,
		SituationId: call.SituationID,
		// TODO: Map History, Priority, Notes if they become relevant
	}

	// Map nullable timestamps
	if call.CallStartTime != nil {
		protoCall.CallStartTime = timestamppb.New(*call.CallStartTime)
	}
	if call.CallEndTime != nil {
		protoCall.CallEndTime = timestamppb.New(*call.CallEndTime)
	}
	if call.LastHoldStart != nil {
		protoCall.LastHoldStart = timestamppb.New(*call.LastHoldStart)
	}

	return protoCall
}

// GetAssetHeldCalls returns calls that an asset has placed on hold.
func (uc *cellularCallUsecase) GetAssetHeldCalls(
	ctx context.Context,
	req *conversationv1.GetAssetHeldCallsRequest,
) (*conversationv1.GetAssetHeldCallsResponse, error) {
	if req.AssetId == "" {
		return nil, errors.New("asset ID is required")
	}
	heldCalls, err := uc.callQueueRepo.GetHeldCalls(ctx, req.AssetId)
	if err != nil {
		return nil, fmt.Errorf("failed to get held calls: %w", err)
	}
	protoHeldCalls := make([]*conversationv1.QueuedCall, len(heldCalls))
	for i := range heldCalls {
		// Use the helper function for mapping
		protoHeldCalls[i] = mapRepoCallToProtoCall(&heldCalls[i])
	}
	return &conversationv1.GetAssetHeldCallsResponse{
		HeldCalls: protoHeldCalls,
	}, nil
}

// ResumeCall connects an asset to a call that was previously on hold.
func (uc *cellularCallUsecase) ResumeCall(
	ctx context.Context,
	req *conversationv1.ResumeCallRequest,
) (*conversationv1.ResumeCallResponse, error) {
	transaction := middleware.StartTransactionWithTraceContext(ctx, "CellularCall/ResumeCall")
	defer transaction.Finish()
	ctx = transaction.Context()
	span := transaction
	span.Op = rpcServerOperation
	span.Description = "CellularCall.ResumeCall"
	span.SetTag("rpc.service", "CellularCall")
	span.SetTag("rpc.method", "ResumeCall")
	span.SetTag("service", "communications-service")
	span.SetTag("call_sid", req.CallSid)
	span.SetTag("asset_id", req.AssetId)

	log.Printf("ResumeCall: Asset %s resuming call %s", req.AssetId, req.CallSid)
	orgTwilioDetails, err := uc.callQueueRepo.GetOrgTwilioDetails(ctx)
	if err != nil {
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		return nil, fmt.Errorf("failed to get org twilio details: %w", err)
	}

	if req.CallSid == "" {
		err := errors.New("call SID is required")
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		return nil, err
	}
	if req.AssetId == "" {
		err := errors.New("asset ID is required")
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		return nil, err
	}

	// Update call state in repository
	if err := uc.callQueueRepo.ResumeCall(ctx, req.CallSid, req.AssetId); err != nil {
		return nil, fmt.Errorf("failed to update call state: %w", err)
	}

	// Simple session-based identity generation - session suffix is required
	sessionSuffix := req.CurrentSessionSuffix
	if sessionSuffix == "" {
		return nil, fmt.Errorf("session suffix is required for call resume")
	}

	log.Printf("Resuming call %s for asset %s with session suffix: %s", req.CallSid, req.AssetId, sessionSuffix)

	// Generate client identity using current session suffix
	clientIdentity, err := uc.formatAgentIdentityWithSession(ctx, req.AssetId, sessionSuffix)
	if err != nil {
		return nil, fmt.Errorf("failed to resume call %s: unable to format identity for asset %s: %w", req.CallSid, req.AssetId, err)
	}

	log.Printf("Resuming call %s with identity: %s", req.CallSid, clientIdentity)

	// Generate client dial TwiML with the appropriate identity
	clientTwiML, err := twiml.ClientDialResponse(clientIdentity, orgTwilioDetails.TwilioNumber)
	if err != nil {
		return nil, fmt.Errorf("failed to generate client dial TwiML: %w", err)
	}

	// Use the Twilio client to modify the call directly
	if err := uc.twilioClient.ModifyCall(ctx, req.CallSid, clientTwiML); err != nil {
		return nil, fmt.Errorf("failed to resume call: %w", err)
	}

	span.SetTag("rpc.status", "success")
	return &conversationv1.ResumeCallResponse{Success: true}, nil
}

// EndCall terminates an active call.
func (uc *cellularCallUsecase) EndCall(
	ctx context.Context,
	req *conversationv1.EndCallRequest,
) (*conversationv1.EndCallResponse, error) {
	transaction := middleware.StartTransactionWithTraceContext(ctx, "CellularCall/EndCall")
	defer transaction.Finish()
	ctx = transaction.Context()
	span := transaction
	span.Op = rpcServerOperation
	span.Description = "CellularCall.EndCall"
	span.SetTag("rpc.service", "CellularCall")
	span.SetTag("rpc.method", "EndCall")
	span.SetTag("service", "communications-service")
	span.SetTag("call_sid", req.CallSid)
	span.SetTag("asset_id", req.AssetId)

	log.Printf("EndCall: Ending call %s for asset %s", req.CallSid, req.AssetId)
	if req.CallSid == "" {
		err := errors.New("call SID is required")
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		return nil, err
	}

	// Update internal state by ending the call in the repository
	err := uc.callQueueRepo.EndCall(ctx, req.CallSid)
	if err != nil {
		log.Printf("Warning: Error ending call in repo: %v", err)
		// return nil, fmt.Errorf("failed to end call: %w", err)
	}
	log.Printf("Call %s ended in repository successfully", req.CallSid)

	// Generate the Hangup TwiML response
	hangupXML, err := twiml.HangupResponse()
	if err != nil {
		log.Printf("Error generating hangup TwiML: %v", err)
		return nil, fmt.Errorf("failed to generate hangup TwiML: %w", err)
	}

	// Use the Twilio client to modify the call, sending the Hangup TwiML
	if err := uc.twilioClient.ModifyCall(ctx, req.CallSid, hangupXML); err != nil {
		log.Printf("Error modifying call to hangup: %v", err)
		return nil, fmt.Errorf("failed to end call via Twilio: %w", err)
	}

	span.SetTag("rpc.status", "success")
	log.Printf("Call %s terminated via Twilio successfully", req.CallSid)
	return &conversationv1.EndCallResponse{
		Success: true,
	}, nil
}

// HandleCall constructs a TwiML response for handling a call.
func (uc *cellularCallUsecase) HandleCall(
	ctx context.Context,
	req *conversationv1.HandleCallRequest,
) (*conversationv1.HandleCallResponse, error) {
	var responseXML string
	var err error

	orgTwilioDetails, err := uc.callQueueRepo.GetOrgTwilioDetails(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get org twilio details: %w", err)
	}

	// Get action from request attributes if available
	action := ""
	if req.Attributes != nil && req.Attributes["action"] != "" {
		action = req.Attributes["action"]
	}

	// If we have a dequeue action in attributes, handle it that way
	const actionDequeue = "dequeue"

	if action == actionDequeue || req.Flow == actionDequeue {
		queueName := ""
		if req.Attributes != nil && req.Attributes["queue"] != "" {
			queueName = req.Attributes["queue"]
		}
		if queueName == "" {
			queueName = uc.twilioQueueName
		}

		responseXML, err = twiml.DequeueResponse(queueName)
		if err != nil {
			return nil, fmt.Errorf("failed to generate dequeue TwiML: %w", err)
		}
	} else {
		switch req.Flow {
		case "outbound":
			// Outbound call: Dial the provided number.
			if req.To == "" || req.To == orgTwilioDetails.TwilioNumber {
				return nil, errors.New("invalid 'to' parameter for outbound call")
			}

			responseXML, err = twiml.OutboundDialResponse(req.To, orgTwilioDetails.TwilioNumber)
			if err != nil {
				return nil, fmt.Errorf("failed to generate outbound TwiML: %w", err)
			}

		case "client-inbound":
			// Client inbound call: Dial the client device.
			if req.Caller == "" {
				return nil, errors.New("missing 'caller' parameter for client inbound call")
			}

			responseXML, err = twiml.ClientDialResponse(req.To, req.Caller)
			if err != nil {
				return nil, fmt.Errorf("failed to generate client inbound TwiML: %w", err)
			}

		default:
			// For backward compatibility, fall back to legacy logic if Flow is not provided.
			switch {
			case req.To != "" && req.To != orgTwilioDetails.TwilioNumber:
				// Treat as outbound call.
				responseXML, err = twiml.OutboundDialResponse(req.To, orgTwilioDetails.TwilioNumber)
				if err != nil {
					return nil, fmt.Errorf("failed to generate outbound TwiML: %w", err)
				}
			case req.Caller != "":
				// Treat as client inbound call.
				responseXML, err = twiml.ClientDialResponse(req.To, req.Caller)
				if err != nil {
					return nil, fmt.Errorf("failed to generate inbound TwiML: %w", err)
				}
			default:
				return nil, errors.New("insufficient call parameters: missing 'to', 'caller', or 'flow'")
			}
		}
	}

	return &conversationv1.HandleCallResponse{
		Twiml: responseXML,
	}, nil
}

// HandleVoiceRequest processes all Twilio voice webhook requests and routes them appropriately
func (uc *cellularCallUsecase) HandleVoiceRequest(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Extract key parameters first for span tagging
	callSID := r.FormValue("CallSid")
	from := r.FormValue("From")
	to := r.FormValue("To")
	action := r.FormValue("action")

	transaction := sentry.StartTransaction(ctx, "POST /voice")
	defer transaction.Finish()
	ctx = transaction.Context()
	span := transaction
	span.Op = httpServerOperation
	span.Description = "POST /voice"
	span.SetTag("http.method", "POST")
	span.SetTag("http.route", "/hero.communications.v1.TwilioWebhookService/voice")
	span.SetTag("service", "communications-service")
	span.SetTag("call_sid", callSID)
	span.SetTag("from", from)
	span.SetTag("to", to)
	span.SetTag("action", action)

	orgTwilioDetails, err := uc.callQueueRepo.GetOrgTwilioDetails(ctx)
	if err != nil {
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		log.Printf("Error getting org twilio details: %v", err)
		http.Error(w, "Error getting org twilio details", http.StatusInternalServerError)
		return
	}

	orgQueueName, err := uc.callQueueRepo.GetTwilioQueueName(ctx)
	if err != nil {
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		log.Printf("Error getting Twilio queue name: %v", err)
		http.Error(w, "Error getting Twilio queue name", http.StatusInternalServerError)
		return
	}

	log.Printf("Voice request: From=%s, To=%s, Action=%s, CallSid=%s",
		from, to, action, callSID)

	// 1. Handle dequeue operation (agent connecting to caller in queue)
	if action == "dequeue" {
		queueName := orgQueueName
		log.Printf("Handling dequeue action for queue: %s", queueName)

		twimlResponse, err := twiml.DequeueResponse(queueName)
		if err != nil {
			log.Printf("Error generating dequeue TwiML: %v", err)
			http.Error(w, "Error generating TwiML", http.StatusInternalServerError)
			return
		}

		w.Header().Set("Content-Type", "text/xml")
		fmt.Fprint(w, twimlResponse)
		return
	}

	// 2. Handle outbound calls from browser
	if action == "outbound" {
		assetID := r.FormValue("assetId")
		destination := r.FormValue("To")

		log.Printf("Handling outbound call request: AssetId=%s, Destination=%s, CallSid=%s",
			assetID, destination, callSID)

		// Create a call record for the outbound call
		outboundCall := data.QueuedCall{
			CallSID:     callSID,
			Caller:      orgTwilioDetails.TwilioNumber,
			CallerName:  "Outbound Call",
			EnqueueTime: time.Now(),
			AssetID:     assetID,
			State:       data.CallStateActive,
			Direction:   data.CallDirectionOutbound,
			Attributes: map[string]string{
				"destination": destination,
				"CallSid":     callSID,
			},
		}

		// Store the outbound call
		if err := uc.callQueueRepo.StoreActiveCall(ctx, outboundCall); err != nil {
			log.Printf("Warning: Failed to store outbound call: %v", err)
		}

		handleReq := &conversationv1.HandleCallRequest{
			Caller:  "client:" + assetID,
			To:      destination,
			Flow:    "outbound",
			CallSid: callSID,
			Attributes: map[string]string{
				"assetId": assetID,
			},
		}

		handleResp, err := uc.HandleCall(ctx, handleReq)
		if err != nil {
			log.Printf("ERROR: Failed to handle outbound call: %v", err)
			fmt.Fprintf(w, "<Response><Say>Error making call: %s</Say></Response>", err.Error())
			return
		}

		log.Printf("Successfully generated TwiML response for outbound call: %s", handleResp.Twiml)
		w.Header().Set("Content-Type", "text/xml")
		fmt.Fprint(w, handleResp.Twiml)
		return
	}

	// 3. Handle inbound calls to queue
	if to == orgTwilioDetails.TwilioNumber {
		log.Printf("Handling inbound call from %s to queue", from)

		queueReq := &conversationv1.QueueCallRequest{
			Caller:     from,
			CallerName: r.FormValue("CallerName"),
			Attributes: map[string]string{"CallSid": callSID},
		}

		queueResp, err := uc.QueueCall(ctx, queueReq)
		if err != nil {
			log.Printf("Error queueing call: %v", err)
			fmt.Fprintf(w, "<Response><Say>Error: %s</Say></Response>", err.Error())
			return
		}

		w.Header().Set("Content-Type", "text/xml")
		fmt.Fprint(w, queueResp.Twiml)
		return
	}

	// 4. Handle client-to-client calls (if implemented)
	if strings.HasPrefix(from, "client:") && strings.HasPrefix(to, "client:") {
		log.Printf("Handling client-to-client call: %s to %s", from, to)

		handleReq := &conversationv1.HandleCallRequest{
			Caller: from,
			To:     to,
			Flow:   "client-inbound",
		}

		handleResp, err := uc.HandleCall(ctx, handleReq)
		if err != nil {
			log.Printf("Error handling client-to-client call: %v", err)
			fmt.Fprintf(w, "<Response><Say>Error connecting call</Say></Response>")
			return
		}

		w.Header().Set("Content-Type", "text/xml")
		fmt.Fprint(w, handleResp.Twiml)
		return
	}

	// 5. Default case - unrecognized call type
	log.Printf("Unrecognized call type, using default queue handler")

	// Default to queue behavior for backward compatibility
	queueReq := &conversationv1.QueueCallRequest{
		Caller:     from,
		CallerName: r.FormValue("CallerName"),
		Attributes: map[string]string{"CallSid": callSID},
	}

	queueResp, err := uc.QueueCall(ctx, queueReq)
	if err != nil {
		log.Printf("Error in default queue handling: %v", err)
		fmt.Fprintf(w, "<Response><Say>We couldn't process your call at this time.</Say></Response>")
		return
	}

	w.Header().Set("Content-Type", "text/xml")
	fmt.Fprint(w, queueResp.Twiml)
	span.SetTag("http.status_code", "200")
}

func (uc *cellularCallUsecase) HandleCallStatusRequest(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	transaction := sentry.StartTransaction(ctx, "POST /callstatus")
	defer transaction.Finish()
	ctx = transaction.Context()
	span := transaction
	span.Op = httpServerOperation
	span.Description = "POST /callstatus"
	span.SetTag("http.method", "POST")
	span.SetTag("http.route", "/hero.communications.v1.TwilioWebhookService/callstatus")
	span.SetTag("service", "communications-service")

	callSid := r.FormValue("CallSid")
	callStatus := r.FormValue("CallStatus")
	direction := r.FormValue("Direction")

	span.SetTag("call_sid", callSid)
	span.SetTag("call_status", callStatus)
	span.SetTag("direction", direction)

	log.Printf("CALLBACK: Call status update for CallSid=%s, Status=%s, Direction=%s",
		callSid, callStatus, direction)

	// For terminated calls, clean up the queue
	if callStatus == "completed" || callStatus == "canceled" ||
		callStatus == "failed" || callStatus == "busy" || callStatus == "no-answer" {

		// Use the existing context since this is a webhook response

		// Clean up the call in the repository
		if err := uc.callQueueRepo.EndCall(ctx, callSid); err != nil {
			log.Printf("Warning: Failed to end call %s in repository: %v", callSid, err)
		} else {
			log.Printf("Call %s marked as ended due to status: %s", callSid, callStatus)
		}
	}

	span.SetTag("http.status_code", "200")
	w.WriteHeader(http.StatusOK)
}

// HandleAgentDialStatusRequest processes Twilio status callbacks for the agent leg of calls
func (uc *cellularCallUsecase) HandleAgentDialStatusRequest(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	transaction := sentry.StartTransaction(ctx, "POST /agent-dial-status")
	defer transaction.Finish()
	ctx = transaction.Context()
	span := transaction
	span.Op = httpServerOperation
	span.Description = "POST /agent-dial-status"
	span.SetTag("http.method", "POST")
	span.SetTag("http.route", "/hero.communications.v1.TwilioWebhookService/twilio/agent-dial-status")
	span.SetTag("service", "communications-service")

	// Parse form data
	err := r.ParseForm()
	if err != nil {
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		log.Printf("DEBUG: Error parsing form: %v", err)
	}

	// Extract key parameters
	agentLegSid := r.FormValue("CallSid") // This is the agent leg SID
	callStatus := r.FormValue("CallStatus")
	customerSid := r.URL.Query().Get("customerSid")     // This is the customer leg SID
	sessionSuffix := r.URL.Query().Get("sessionSuffix") // Session suffix for identity consistency
	callDuration := r.FormValue("CallDuration")

	span.SetTag("agent_leg_sid", agentLegSid)
	span.SetTag("call_status", callStatus)
	span.SetTag("customer_sid", customerSid)
	span.SetTag("session_suffix", sessionSuffix)
	span.SetTag("call_duration", callDuration)

	log.Printf("AGENT DIAL STATUS: AgentLegSid=%s, Status=%s, CustomerSid=%s, SessionSuffix=%s, Duration=%s",
		agentLegSid, callStatus, customerSid, sessionSuffix, callDuration)

	// TODO: Add Twilio request validation in subtask 3.1

	// Validate required parameters
	if customerSid == "" {
		err := errors.New("missing customerSid parameter")
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		log.Printf("Error: Missing customerSid in agent dial status callback")
		http.Error(w, "Missing customerSid parameter", http.StatusBadRequest)
		return
	}

	if agentLegSid == "" || callStatus == "" {
		err := errors.New("missing required parameters")
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		log.Printf("Error: `Missing required parameters` in agent dial status callback")
		http.Error(w, "Missing required parameters", http.StatusBadRequest)
		return
	}

	// Retrieve the call record using the customer SID
	call, found, err := uc.callQueueRepo.GetCallByCallSID(ctx, customerSid)
	if err != nil {
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		log.Printf("Error retrieving call with SID %s: %v", customerSid, err)
		http.Error(w, "Error retrieving call record", http.StatusInternalServerError)
		return
	}

	if !found {
		err := fmt.Errorf("call with SID %s not found", customerSid)
		span.SetTag("error", "true")
		span.SetTag("call_not_found", "true")
		sentry.CaptureException(err)
		log.Printf("Call with SID %s not found", customerSid)
		http.Error(w, "Call not found", http.StatusNotFound)
		return
	}

	// Log the current state before processing
	log.Printf("Call %s current state: %s", customerSid, call.State)

	// Process based on status
	switch callStatus {
	case TwilioCallStatusInitiated, TwilioCallStatusRinging:
		// These events indicate Twilio is attempting to reach the agent.
		// No state change is needed for the customer call record (call) at this point,
		// as it remains in 'pending_selective_assignment'.
		log.Printf("AGENT DIAL STATUS: Agent leg %s for customer call %s is %s.", agentLegSid, customerSid, callStatus)

	case TwilioCallStatusInProgress: // This CallStatus indicates the <Client> leg (agentLegSid) has been answered.
		// This means the <Dial> from customerSid (the parent call) to the agent was successful.
		log.Printf("AGENT DIAL STATUS: Agent leg %s for customer call %s is now 'in-progress' (answered). Current customer call state: %s", agentLegSid, customerSid, call.State)

		// Debug log the call details
		attributesJSON, _ := json.Marshal(call.Attributes)
		log.Printf("DEBUG: Call details before transition: SID=%s, State=%s, AssetID=%s, Attributes=%s",
			call.CallSID, call.State, call.AssetID, string(attributesJSON))

		if call.State == data.CallStatePendingSelectiveAssign {
			// The customer call is in the expected state to be connected to this agent.
			// Transition the customer call to active.
			now := time.Now()
			call.CallStartTime = &now // Mark the start of the conversation.
			call.State = data.CallStateActive
			if call.Attributes == nil {
				call.Attributes = make(map[string]string)
			}
			// Store the agent leg's SID on the customer call record for reference.
			call.Attributes["agentLegSid"] = agentLegSid

			// Store the session suffix from the status callback URL for resume operations
			// The session suffix was passed through the dequeue → TwiML → status callback flow
			if sessionSuffix != "" {
				call.Attributes["sessionSuffix"] = sessionSuffix
				log.Printf("Stored session suffix '%s' for call %s", sessionSuffix, customerSid)
			}

			// Debug log the call details before storing
			attributesJSON2, _ := json.Marshal(call.Attributes)
			log.Printf("DEBUG: Call details after state change, before store: SID=%s, State=%s, AssetID=%s, Attributes=%s",
				call.CallSID, call.State, call.AssetID, string(attributesJSON2))

			if err := uc.callQueueRepo.StoreActiveCall(ctx, call); err != nil {
				log.Printf("Error updating customer call %s to active after agent leg answered: %v", customerSid, err)
				http.Error(w, "Error updating call state", http.StatusInternalServerError)
				return
			}

			// Verify the call was updated by fetching it again
			updatedCall, found, err := uc.callQueueRepo.GetCallByCallSID(ctx, customerSid)
			if err != nil || !found {
				log.Printf("DEBUG: Failed to verify call update: err=%v, found=%v", err, found)
			} else {
				log.Printf("DEBUG: Call after update: SID=%s, State=%s, AssetID=%s",
					updatedCall.CallSID, updatedCall.State, updatedCall.AssetID)
			}

			log.Printf("Customer call %s successfully updated to active, connected to agent leg %s.", customerSid, agentLegSid)
		} else {
			// The customer call is NOT in the 'pending_selective_assignment' state.
			// This implies our system does not expect this agent leg to connect to this customer call now
			// (e.g., the claim might have been reverted by timeout, the call ended, or reassigned).
			// However, Twilio has bridged the agent leg based on earlier TwiML.
			// To maintain consistency with our backend state, we should terminate this unexpected agent leg.
			log.Printf("AGENT DIAL STATUS: Customer call %s is in state '%s', not '%s'. Agent leg %s (%s) answered unexpectedly. Terminating this agent leg.",
				customerSid, call.State, data.CallStatePendingSelectiveAssign, agentLegSid, callStatus)

			hangupTwiML, twimlErr := twiml.HangupResponse()
			if twimlErr != nil {
				log.Printf("Error generating hangup TwiML for unexpected agent leg %s: %v", agentLegSid, twimlErr)
				// If TwiML generation fails, we can't actively hang up. The call might persist until manually ended or Twilio times it out.
			} else {
				if err := uc.twilioClient.ModifyCall(ctx, agentLegSid, hangupTwiML); err != nil {
					log.Printf("Error sending hangup command to unexpected agent leg %s: %v", agentLegSid, err)
				} else {
					log.Printf("Successfully sent hangup command to unexpected agent leg %s.", agentLegSid)
				}
			}
			// The customer call's state (customerSid) remains as it was (e.g., 'waiting', 'ended').
			// We do not acknowledge this agent leg as a valid connection in our primary state logic for the customer call.
		}

	case TwilioCallStatusCompleted, TwilioCallStatusBusy, TwilioCallStatusNoAnswer, TwilioCallStatusFailed, TwilioCallStatusCanceled:
		// These are terminal statuses for the agent leg.
		log.Printf("AGENT DIAL STATUS: Agent leg %s for customer call %s received terminal status: %s. Current customer call state: %s", agentLegSid, customerSid, callStatus, call.State)

		// If the customer call is currently on hold, the termination of this specific agent leg
		// should not automatically end the customer's on-hold session.
		// The customer call remains on hold until explicitly resumed or ended by the agent via other actions.
		if call.State == data.CallStateHold {
			log.Printf("AGENT DIAL STATUS: Agent leg %s terminated with status %s, but customer call %s is on hold. Customer call will remain on hold.", agentLegSid, callStatus, customerSid)
			// Optionally, store information about this specific agent leg termination if needed for audit, but do not change the customer call state from on_hold.
			if call.Attributes == nil {
				call.Attributes = make(map[string]string)
			}
			call.Attributes[fmt.Sprintf("agentLeg_%s_status", agentLegSid)] = callStatus
			if callDuration != "" {
				call.Attributes[fmt.Sprintf("agentLeg_%s_duration", agentLegSid)] = callDuration
			}
			if err := uc.callQueueRepo.StoreActiveCall(ctx, call); err != nil { // Store updated attributes
				log.Printf("Error updating attributes for on-hold call %s after agent leg %s terminated: %v", customerSid, agentLegSid, err)
				// Not returning error to Twilio here, as the primary call is still on hold as intended.
			}
			w.WriteHeader(http.StatusOK)
			return
		}

		// If the call is not on hold, proceed to end it if it's not already ended.
		if call.State != data.CallStateEnded {
			// Set the call end time if not already set
			if call.CallEndTime == nil {
				now := time.Now()
				call.CallEndTime = &now
			}

			// Initialize attributes map if needed
			if call.Attributes == nil {
				call.Attributes = make(map[string]string)
			}

			// Store call duration from Twilio if available
			if callDuration != "" {
				call.Attributes["twilioCallDuration"] = callDuration
			}

			// Store error information if available
			errorCode := r.FormValue("ErrorCode")
			if errorCode != "" {
				call.Attributes["twilioErrorCode"] = errorCode
				call.Attributes["twilioErrorMessage"] = r.FormValue("ErrorMessage")
			}

			// Store call status
			call.Attributes["twilioCallStatus"] = callStatus

			// End the call in the repository
			if err := uc.callQueueRepo.EndCall(ctx, customerSid); err != nil {
				log.Printf("Error ending call %s: %v", customerSid, err)
				http.Error(w, "Error updating call state", http.StatusInternalServerError)
				return
			}

			log.Printf("Call %s ended with status %s", customerSid, callStatus)
		} else {
			log.Printf("Call %s already in ended state", customerSid)
		}

	default:
		log.Printf("Unhandled call status '%s' for call %s", callStatus, customerSid)
	}

	// Return 200 OK to acknowledge receipt
	span.SetTag("http.status_code", "200")
	w.WriteHeader(http.StatusOK)
}

// RevertSelectiveClaim handles reverting a call from pending_selective_assignment to waiting
func (uc *cellularCallUsecase) RevertSelectiveClaim(
	ctx context.Context,
	req *conversationv1.RevertSelectiveClaimRequest,
) (*conversationv1.RevertSelectiveClaimResponse, error) {
	transaction := middleware.StartTransactionWithTraceContext(ctx, "CellularCall/RevertSelectiveClaim")
	defer transaction.Finish()
	ctx = transaction.Context()
	span := transaction
	span.Op = rpcServerOperation
	span.Description = "CellularCall.RevertSelectiveClaim"
	span.SetTag("rpc.service", "CellularCall")
	span.SetTag("rpc.method", "RevertSelectiveClaim")
	span.SetTag("service", "communications-service")
	span.SetTag("call_sid", req.CallSid)

	// Validate input
	if req.CallSid == "" {
		err := errors.New("call SID is required")
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		log.Printf("Error: Missing call SID in RevertSelectiveClaim request")
		return nil, connect.NewError(connect.CodeInvalidArgument, err)
	}

	log.Printf("Attempting to revert selective claim for call %s", req.CallSid)

	// Call repository method to revert the call state
	reverted, err := uc.callQueueRepo.RevertSelectiveClaim(ctx, req.CallSid)
	if err != nil {
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		log.Printf("Error reverting selective claim for call %s: %v", req.CallSid, err)
		return nil, connect.NewError(connect.CodeInternal, err)
	}

	span.SetTag("reverted", fmt.Sprintf("%t", reverted))

	// Log the result
	if reverted {
		log.Printf("Successfully reverted selective claim for call %s", req.CallSid)
	} else {
		log.Printf("Call %s was not in pending_selective_assignment state, no changes made", req.CallSid)
	}

	// Return success response
	span.SetTag("rpc.status", "success")
	return &conversationv1.RevertSelectiveClaimResponse{
		Success:  true,
		Reverted: reverted,
	}, nil
}

// formatAgentIdentityWithSession converts a raw asset ID to the formatted identity used by clients with optional session suffix
func (uc *cellularCallUsecase) formatAgentIdentityWithSession(ctx context.Context, assetID string, sessionSuffix string) (string, error) {
	if assetID == "" {
		return "", fmt.Errorf("empty asset ID")
	}

	const prefixLength = 8
	var idPrefixPart string
	var namePartVal string

	// Use assetID for prefix part
	if len(assetID) > prefixLength {
		idPrefixPart = assetID[:prefixLength]
	} else {
		idPrefixPart = assetID // Use full assetID if shorter than prefixLength
	}

	// Try to get asset name for the name part
	getAssetReq := &assetsv2.GetAssetRequest{Id: assetID}
	assetResp, err := uc.assetsClient.GetAsset(ctx, connect.NewRequest(getAssetReq))

	var fetchedAssetName string
	if err != nil {
		log.Printf("Warning: Failed to fetch asset name for %s, using asset ID as fallback for name part: %v", assetID, err)
		fetchedAssetName = assetID // Use asset ID as fallback for name part
	} else {
		fetchedAssetName = assetResp.Msg.Asset.Name
		if fetchedAssetName == "" {
			log.Printf("Warning: Asset %s has empty name, using asset ID as fallback for name part", assetID)
			fetchedAssetName = assetID // Use asset ID as fallback for name part
		}
	}

	// Sanitize the fetched asset name (which could be the assetID fallback) for Twilio
	namePartVal = sanitizeTwilioIdentity(fetchedAssetName)

	// Construct the identity string in the new format: idPrefixPart-namePartVal-sessionSuffix
	var identity string
	if sessionSuffix != "" {
		identity = fmt.Sprintf("%s-%s-%s", idPrefixPart, namePartVal, sessionSuffix)
	} else {
		// If no sessionSuffix, omit it and its preceding separator from the format
		identity = fmt.Sprintf("%s-%s", idPrefixPart, namePartVal)
	}

	return identity, nil
}

// HandleConnectAgentTwiMLRequest generates TwiML to connect an agent to a specific customer call
func (uc *cellularCallUsecase) HandleConnectAgentTwiMLRequest(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	transaction := sentry.StartTransaction(ctx, "POST /twiml/connectAgent")
	defer transaction.Finish()
	ctx = transaction.Context()
	span := transaction
	span.Op = httpServerOperation
	span.Description = "POST /twiml/connectAgent"
	span.SetTag("http.method", "POST")
	span.SetTag("http.route", "/hero.communications.v1.TwilioWebhookService/twiml/connectAgent")
	span.SetTag("service", "communications-service")

	log.Printf("HandleConnectAgentTwiMLRequest: Received request. Method: %s, URL: %s", r.Method, r.URL.String())
	parsedQueryParams := r.URL.Query()

	// Retrieve parameters using the correct key "agentId"
	agentID := parsedQueryParams.Get("agentId")
	customerCallSID := parsedQueryParams.Get("customerSid")
	sessionSuffix := parsedQueryParams.Get("sessionSuffix")

	span.SetTag("agent_id", agentID)
	span.SetTag("customer_sid", customerCallSID)
	span.SetTag("session_suffix", sessionSuffix)

	log.Printf("HandleConnectAgentTwiMLRequest: Retrieved agentId: '%s', customerSid: '%s', sessionSuffix: '%s'", agentID, customerCallSID, sessionSuffix)

	if agentID == "" || customerCallSID == "" {
		err := errors.New("missing required parameters")
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		log.Printf("HandleConnectAgentTwiMLRequest: Missing required parameters: agentId_is_empty=%t, customerSid_is_empty=%t. Values: agentId='%s', customerSid='%s'", agentID == "", customerCallSID == "", agentID, customerCallSID)
		http.Error(w, "Missing required parameters", http.StatusBadRequest)
		return
	}

	// Get org Twilio details for caller ID
	orgTwilioDetails, err := uc.callQueueRepo.GetOrgTwilioDetails(r.Context())
	if err != nil {
		log.Printf("Error getting org Twilio details: %v", err)
		http.Error(w, "Error generating TwiML", http.StatusInternalServerError)
		return
	}

	// Look up org twilio users
	getOrgAPIUserPrivateRequest := &orgs.GetOrgAPIUserPrivateByIdRequest{
		UserId: orgTwilioDetails.TwilioApiUserId,
	}
	basicTwilioUserAuth, err := uc.orgClient.GetOrgAPIUserPrivateById(r.Context(), connect.NewRequest(getOrgAPIUserPrivateRequest))
	if err != nil {
		log.Printf("Error getting org API user: %v", err)
		http.Error(w, "Error generating TwiML", http.StatusInternalServerError)
		return
	}

	// Build the status callback URL with the customer SID and session suffix
	publicCommunicationsServiceURL := uc.commsServerPublicDomain
	basicUserAuth := basicTwilioUserAuth.Msg.OrgApiUser.Username + ":" + basicTwilioUserAuth.Msg.OrgApiUser.RawPassword
	statusCallbackURL := fmt.Sprintf("https://%s@%s/hero.communications.v1.TwilioWebhookService/twilio/agent-dial-status?customerSid=%s&sessionSuffix=%s", basicUserAuth, publicCommunicationsServiceURL, customerCallSID, sessionSuffix)

	// Format the agentID using the same logic as token generation to match client identity
	// Include session suffix if provided to ensure consistent identity formatting
	agentIdentitySpan := sentry.StartSpan(ctx, "communications.format_agent_identity")
	agentIdentitySpan.Description = "Communications.FormatAgentIdentity"
	agentIdentitySpan.SetTag("operation", "format_agent_identity")
	agentIdentitySpan.SetTag("agent_id", agentID)
	agentIdentitySpan.SetTag("session_suffix", sessionSuffix)
	formattedAgentID, err := uc.formatAgentIdentityWithSession(agentIdentitySpan.Context(), agentID, sessionSuffix)
	agentIdentitySpan.Finish()
	if err != nil {
		agentIdentitySpan.SetTag("error", "true")
		agentIdentitySpan.SetTag("fallback_used", "true")
		sentry.CaptureException(err)
		log.Printf("Warning: Failed to format agent identity for %s, using raw ID: %v", agentID, err)
		formattedAgentID = agentID // Fallback to raw ID
	} else {
		agentIdentitySpan.SetTag("formatted_identity", formattedAgentID)
	}

	// Generate the TwiML
	twimlGenerationSpan := sentry.StartSpan(ctx, "communications.generate_agent_connect_twiml")
	twimlGenerationSpan.Description = "Communications.GenerateAgentConnectTwiML"
	twimlGenerationSpan.SetTag("operation", "generate_agent_connect_twiml")
	twimlGenerationSpan.SetTag("agent_identity", formattedAgentID)
	twimlGenerationSpan.SetTag("customer_sid", customerCallSID)
	// Use formatted agent ID to match the client identity from token generation
	twimlXML, err := twiml.AgentConnectTwiML(formattedAgentID, customerCallSID, orgTwilioDetails.TwilioNumber, statusCallbackURL)
	twimlGenerationSpan.Finish()
	if err != nil {
		twimlGenerationSpan.SetTag("error", "true")
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		log.Printf("Error generating TwiML: %v", err)
		http.Error(w, "Error generating TwiML", http.StatusInternalServerError)
		return
	} else {
		twimlGenerationSpan.SetTag("twiml_length", fmt.Sprintf("%d", len(twimlXML)))
	}

	// Set content type and write response
	w.Header().Set("Content-Type", "text/xml")
	_, err = w.Write([]byte(twimlXML))
	if err != nil {
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		log.Printf("Error writing TwiML response: %v", err)
		return
	}
	span.SetTag("http.status_code", "200")
}

// HandleWaitHoldRequest generates smart TwiML for queue wait and hold messaging
func (uc *cellularCallUsecase) HandleWaitHoldRequest(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	action := r.URL.Query().Get("action") // "enqueue" or "hold"

	transaction := sentry.StartTransaction(ctx, "POST /waithold")
	defer transaction.Finish()
	ctx = transaction.Context()
	span := transaction
	span.Op = httpServerOperation
	span.Description = "POST /waithold"
	span.SetTag("http.method", "POST")
	span.SetTag("http.route", "/hero.communications.v1.TwilioWebhookService/waithold")
	span.SetTag("service", "communications-service")
	span.SetTag("action", action)

	log.Printf("HandleWaitHoldRequest: action=%s", action)

	var twimlResponse string
	var err error

	switch action {
	case "enqueue":
		// Get org name for dynamic messaging from existing Twilio details
		orgTwilioDetails, orgErr := uc.callQueueRepo.GetOrgTwilioDetails(ctx)
		var orgName string
		if orgErr != nil {
			log.Printf("Warning: Failed to get org details for dynamic messaging: %v", orgErr)
			orgName = "" // Will use fallback message
		} else {
			orgName = orgTwilioDetails.OrgName
		}
		twimlResponse, err = twiml.EnqueueWaitResponse(orgName)
	case "hold":
		// Construct the hold URL for looping (same URL that called this endpoint)
		holdURL := fmt.Sprintf("https://%s%s", uc.commsServerPublicDomain, r.URL.String())
		twimlResponse, err = twiml.HoldWaitResponse(holdURL)
	default:
		err := fmt.Errorf("invalid action parameter: %s", action)
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		log.Printf("Invalid action parameter: %s", action)
		http.Error(w, "Invalid action parameter", http.StatusBadRequest)
		return
	}

	if err != nil {
		span.SetTag("error", "true")
		sentry.CaptureException(err)
		log.Printf("Error generating wait/hold TwiML: %v", err)
		http.Error(w, "Error generating TwiML", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "text/xml")
	fmt.Fprint(w, twimlResponse)
	span.SetTag("http.status_code", "200")
}
