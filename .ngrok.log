t=2025-07-09T09:59:15-0700 lvl=info msg="no configuration paths supplied"
t=2025-07-09T09:59:15-0700 lvl=info msg="using configuration at default config path" path="/Users/<USER>/Library/Application Support/ngrok/ngrok.yml"
t=2025-07-09T09:59:15-0700 lvl=info msg="open config file" path="/Users/<USER>/Library/Application Support/ngrok/ngrok.yml" err=nil
t=2025-07-09T09:59:15-0700 lvl=info msg="starting web service" obj=web addr=127.0.0.1:4040 allow_hosts=[]
t=2025-07-09T09:59:16-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T09:59:16-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T09:59:16-0700 lvl=info msg="started tunnel" obj=tunnels name=command_line addr=http://localhost:9084 url=https://e65db4b67acd.ngrok-free.app
t=2025-07-09T09:59:16-0700 lvl=info msg="update available" obj=updater
t=2025-07-09T09:59:16-0700 lvl=info msg="join connections" obj=join id=39bec73e9d4b l=[::1]:9084 r=**************:53103
