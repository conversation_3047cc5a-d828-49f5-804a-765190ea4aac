import * as Sentry from "@sentry/nextjs";

// Sentry Edge Config to handle Edge functions (middleware, edge API routes)

// Initialize Sentry in production OR when NEXT_PUBLIC_ENABLE_SENTRY is true for testing
const isProduction = process.env.NODE_ENV === 'production';
const enableSentryInDev = process.env.NEXT_PUBLIC_ENABLE_SENTRY === 'true';

if (isProduction || enableSentryInDev) {
  Sentry.init({
    dsn: "https://<EMAIL>/4509079520477185",
    environment: isProduction ? "production" : "development",
    tracesSampleRate: isProduction ? 0.1 : 1,
    debug: enableSentryInDev,
  });
}
