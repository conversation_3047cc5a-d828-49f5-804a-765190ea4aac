"use client";

import React, {
  createContext,
  useContext,
  useEffect,
  useRef,
  useCallback,
  useState,
} from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Device, Call } from '@twilio/voice-sdk';

import {
  getCellularCallAccessToken,
  dequeueCall,
  dequeueCallBySid,
  holdCall,
  resumeCall,
  endCall,
  getQueueStatus,
  getAssetHeldCalls,
  revertSelectiveClaim,
} from '@/app/apis/services/communications/cellularcall/endpoints';

import {
  GetCellularCallAccessTokenRequest,
  GetCellularCallAccessTokenResponse,
  DequeueCallRequest,
  DequeueCallResponse,
  EndCallRequest,
  HoldCallRequest,
  QueuedCall,
  ResumeCallRequest,
  GetQueueStatusRequest,
  GetQueueStatusResponse,
  GetAssetHeldCallsRequest,
  GetAssetHeldCallsResponse,
  DequeueCallBySidResponse,
} from 'proto/hero/communications/v1/conversation_pb';

import { isoStringToProtoTimestamp } from "proto/utils";

import { useDispatcher } from '../User/DispatcherContext';
import { useCallMetrics } from './useCallMetrics';

// -----------------------------------------------------------------------------
// Constants & Logging Helpers
// -----------------------------------------------------------------------------
const TOKEN_REFRESH_BUFFER = 3600; // seconds (1 hour)
const HELD_CALLS_REFRESH_INTERVAL = 10000; // ms (refresh held calls every 10 seconds)
const TOKEN_TIME_TO_LIVE = 28800; // seconds (8 hour)

const logInfo = (source: string, message: string, payload?: unknown) => {
    console.log(
      `[CallContext][${source}][${new Date().toISOString()}] ${message}`,
      payload ?? ''
    );
};

const logError = (source: string, message: string, payload?: unknown) => {
  console.error(
    `[CallContext][${source}][${new Date().toISOString()}] ${message}`,
    payload ?? ''
  );
};

// -----------------------------------------------------------------------------
// Helper: Decode Token Expiry
// -----------------------------------------------------------------------------
const getTokenExpiry = (token: string): number | null => {
  try {
    const parts = token.split('.');
    if (parts.length < 2) {
      throw new Error('Invalid token format');
    }
    const payload = JSON.parse(atob(parts[1]));
    logInfo("TokenDecode", "Decoded token payload", { payload });
    return payload.exp * 1000;
  } catch (error) {
    logError("TokenDecode", "Failed to decode token expiry", { error });
    return null;
  }
};

// -----------------------------------------------------------------------------
// Custom Hook: Token Management
// -----------------------------------------------------------------------------
const useTokenManagement = (assetId: string | undefined) => {
  const [token, setToken] = useState<string | undefined>(undefined);
  const [tokenExpiry, setTokenExpiry] = useState<number>(0);
  const sessionSuffixRef = useRef(crypto.randomUUID().substring(0, 8)); // Generate a unique session suffix
  const { mutate: fetchToken } = useMutation<
    GetCellularCallAccessTokenResponse,
    Error,
    GetCellularCallAccessTokenRequest
  >({
    mutationFn: (data) => getCellularCallAccessToken(data),
    retry: 3,
    onSuccess: (response) => {
      logInfo("TokenFetch", "Token retrieved successfully", {
        token: response.token,
      });
      setToken(response.token);
    },
    onError: (err) => {
      logError("TokenFetch", "Token retrieval error", { error: err });
    },
  });

  // Initial token fetch if not set
  useEffect(() => {
    if (assetId && !token) {
      logInfo("TokenEffect", "Fetching token because none is set", { assetId });
      fetchToken({
        $typeName: 'hero.conversation.v1.GetCellularCallAccessTokenRequest',
        identity: assetId,
        expire: TOKEN_TIME_TO_LIVE,
        sessionSuffix: sessionSuffixRef.current,
      });
    }
  }, [assetId, token, fetchToken]);

  // Refresh token based on expiry
  useEffect(() => {
    if (!assetId || !token) return;
    const expiryTime = getTokenExpiry(token);
    if (!expiryTime) return;
    setTokenExpiry(expiryTime);
    logInfo("TokenRefresh", "Token expiry set", { expiryTime });

    const refreshDelay =
      expiryTime - Date.now() - TOKEN_REFRESH_BUFFER * 1000;
    logInfo("TokenRefresh", "Calculated refresh delay", { refreshDelay });
    const timeoutId = setTimeout(() => {
      logInfo("TokenRefresh", "Refreshing Twilio access token", { assetId });
      fetchToken({
        $typeName: 'hero.conversation.v1.GetCellularCallAccessTokenRequest',
        identity: assetId,
        expire: TOKEN_TIME_TO_LIVE,
        sessionSuffix: sessionSuffixRef.current,
      });
    }, Math.max(refreshDelay, 0));
    return () => clearTimeout(timeoutId);
  }, [token, assetId, fetchToken]);

  // Expose a refreshToken function that forces refetching the token
  const refreshToken = () => {
    if (!assetId) return;
    logInfo("TokenRefresh", "Force refreshing token", { assetId });
    fetchToken({
      $typeName: 'hero.conversation.v1.GetCellularCallAccessTokenRequest',
      identity: assetId,
      expire: TOKEN_TIME_TO_LIVE,
      sessionSuffix: sessionSuffixRef.current,
    });
  };

  return { token, tokenExpiry, refreshToken, sessionSuffixRef };
};


// -----------------------------------------------------------------------------
// Custom Hook: Device Management
// -----------------------------------------------------------------------------
const useDeviceManagement = (
  token: string | undefined,
  assetId: string | undefined,
  refreshToken: () => void,
  trackDeviceHealth: (metadata: any) => void) => {
  const [device, setDevice] = useState<Device | null>(null);
  const [deviceStatus, setDeviceStatus] = useState<DeviceStatus>('offline');
  const [deviceError, setDeviceError] = useState<Error | null>(null);
  const [microphoneAccessFailed, setMicrophoneAccessFailed] = useState(false);
  const [isMicrophoneEnabled, setIsMicrophoneEnabled] = useState(false);
  const deviceRef = useRef<Device | null>(null);
  const initializedAssetRef = useRef<string | null>(null);
  const isDeviceReinitializingRef = useRef(false);

  // Clean up a device instance
  const cleanupDevice = (deviceInstance: Device | null) => {
    if (!deviceInstance) return;
    try {
      deviceInstance.disconnectAll();
      deviceInstance.destroy();
      logInfo("DeviceCleanup", "Device cleanup executed");
    } catch (error) {
      logError("DeviceCleanup", "Error during device cleanup", { error });
    }
    setDevice(null);
    deviceRef.current = null;
  };

  // Initialize and register a new device instance
  const initializeDeviceInstance = async (twilioToken: string) => {
    const initStartTime = Date.now();
    let retryCountForMicrophoneAccess = 0;
    const maxRetriesForMicrophoneAccess = 3;
    
    while (retryCountForMicrophoneAccess < maxRetriesForMicrophoneAccess) {
      try {
        // First try to get microphone access
        try {
          await navigator.mediaDevices.getUserMedia({ audio: true });
          setIsMicrophoneEnabled(true);
          setMicrophoneAccessFailed(false);
          logInfo("DeviceInit", "Microphone access granted");
          
          // Track successful microphone access
          trackDeviceHealth({
            operation: 'microphone_access',
            deviceStatus: 'microphone_granted',
            microphoneAccess: true,
            retryCount: retryCountForMicrophoneAccess
          });
        } catch (micError) {
          logError("DeviceInit", `Microphone access failed (attempt ${retryCountForMicrophoneAccess + 1}/${maxRetriesForMicrophoneAccess})`, { error: micError });
          retryCountForMicrophoneAccess++;
          
          // Track microphone access failure
          trackDeviceHealth({
            operation: 'microphone_access',
            deviceStatus: 'microphone_retry',
            microphoneAccess: false,
            retryCount: retryCountForMicrophoneAccess,
            error: (micError as Error).message
          });
          
          if (retryCountForMicrophoneAccess < maxRetriesForMicrophoneAccess) {
            logInfo("DeviceInit", `Retrying microphone access in 1 second...`);
            await new Promise(resolve => setTimeout(resolve, 1000));
            continue; // Skip the rest of the loop and retry
          } else {
            // All retries failed for microphone
            logError("DeviceInit", "Microphone access failed after maximum retries");
            setIsMicrophoneEnabled(false);
            setMicrophoneAccessFailed(true);
            setDeviceError(new Error(`Failed to access microphone after ${maxRetriesForMicrophoneAccess} attempts`));
            setDeviceStatus('offline');
            
            // Track final microphone failure
            trackDeviceHealth({
              operation: 'microphone_access',
              deviceStatus: 'microphone_failed',
              microphoneAccess: false,
              retryCount: retryCountForMicrophoneAccess,
              error: `Failed after ${maxRetriesForMicrophoneAccess} attempts`,
              duration: Date.now() - initStartTime
            });
            
            return; // Exit function
          }
        }
        
        const newDevice = new Device(twilioToken);

        // Disable outgoing and incoming sounds by default
        if (newDevice.audio) {
          newDevice.audio.outgoing(false);
          newDevice.audio.incoming(false);
        }
  
        // Device event listeners
        newDevice.on('registering', () => {
          logInfo("DeviceEvent", "Device registering");
          setDeviceStatus('registering');
          trackDeviceHealth({
            operation: 'device_status_change',
            deviceStatus: 'registering'
          });
        });
        newDevice.on('registered', () => {
          logInfo("DeviceEvent", "Device ready");
          setDeviceStatus('ready');
          trackDeviceHealth({
            operation: 'device_status_change',
            deviceStatus: 'ready'
          });
        });
        newDevice.on('unregistered', () => {
          logInfo("DeviceEvent", "Device unregistered");
          setDeviceStatus('offline');
          trackDeviceHealth({
            operation: 'device_status_change',
            deviceStatus: 'offline'
          });
        });
        newDevice.on('destroyed', () => {
          logInfo("DeviceEvent", "Device destroyed");
          setDeviceStatus('destroyed');
          trackDeviceHealth({
            operation: 'device_status_change',
            deviceStatus: 'destroyed'
          });
        });
        newDevice.on('error', (error, call) => {
          const errorMessage = error instanceof Error ? error.message : String(error);
          logError("DeviceEvent", "Device error occurred", { error, call });
          setDeviceError(error instanceof Error ? error : new Error(String(error)));
          setDeviceStatus('offline');
          trackDeviceHealth({
            operation: 'device_error',
            deviceStatus: 'error',
            error: errorMessage
          });
        });
        newDevice.on('incoming', (incomingCall) => {
          logInfo("DeviceEvent", "Incoming call received", { incomingCall });
        });
        newDevice.on('tokenWillExpire', () => {
          logInfo("DeviceEvent", "Token will expire soon");
          trackDeviceHealth({
            operation: 'token_will_expire',
            deviceStatus: 'token_expiring'
          });
          if (!assetId) return;
          // Note: token refresh should be handled by useTokenManagement.
          // This is a failsafe mechanism.
          refreshToken();
        });
        newDevice.register();
        deviceRef.current = newDevice;
        setDevice(newDevice);
        
        // Track successful device initialization
        trackDeviceHealth({
          operation: 'device_initialization',
          deviceStatus: 'initialized',
          duration: Date.now() - initStartTime
        });
        
        return;         
      } catch (error) {
        const errorMessage = `Failed to create Twilio device: ${(error as Error).message}`;
        logError("DeviceInit", "Error during device initialization", { error });
        setDeviceError(new Error(errorMessage));
        setDeviceStatus('offline');
        
        // Track device initialization failure
        trackDeviceHealth({
          operation: 'device_initialization',
          deviceStatus: 'failed',
          error: errorMessage,
          duration: Date.now() - initStartTime
        });
        
        return; 
      }
    }
  };

  // (Re)initialize device when assetId or token changes
  useEffect(() => {
    if (!assetId || !token) return;
    if (initializedAssetRef.current === assetId) return;

    cleanupDevice(deviceRef.current);
    initializedAssetRef.current = assetId;

    logInfo("DeviceInit", "Initializing Twilio Device", { token });
    initializeDeviceInstance(token);

    return () => {
      cleanupDevice(deviceRef.current);
    };
  }, [assetId, token]);

  // Reinitialize device if status indicates a need to do so
  useEffect(() => {
    if (!assetId || !token) return;
    if ((deviceStatus === 'offline' || deviceStatus === 'destroyed') && !isDeviceReinitializingRef.current) {
      isDeviceReinitializingRef.current = true;
      logInfo("DeviceReinit", "Device status indicates reinitialization", { deviceStatus });
      cleanupDevice(deviceRef.current);
      initializeDeviceInstance(token)
        .catch((error) => {
          logError("DeviceReinit", "Failed to reinitialize device", { error });
        })
        .finally(() => {
          isDeviceReinitializingRef.current = false;
        });
    }
  }, [deviceStatus, assetId, token]);

  // Update device token when token or device changes
  useEffect(() => {
    if (!device || !token) return;
    if (deviceStatus !== 'ready') {
      logInfo("DeviceUpdate", "Device not ready; token update postponed", { deviceStatus });
      return;
    }
    try {
      logInfo("DeviceUpdate", "Updating device token");
      device.updateToken(token);
    } catch (error) {
      logError("DeviceUpdate", "Error updating device token", { error });
    }
  }, [device, token, deviceStatus]);

  return { device, deviceStatus, deviceError, isMicrophoneEnabled, microphoneAccessFailed, setDevice, setDeviceStatus };
};

// -----------------------------------------------------------------------------
// Type Definitions & Context Interface
// -----------------------------------------------------------------------------
export type CallStatus = 'idle' | 'connecting' | 'active' | 'held' | 'muted';
export type DeviceStatus =
  | 'offline'
  | 'connecting'
  | 'registering'
  | 'ready'
  | 'destroyed';

export interface CallOperations {
  dequeueAndConnectCallAPI: () => Promise<{
    connection: Call;
    callData: DequeueCallResponse;
  }>;
  placeCallOnHoldAPI: (callSid: string, connection: Call) => Promise<boolean>;
  resumeCallAPI: (callSid: string) => Promise<{ success: boolean }>;
  endCallAPI: (callSid: string, connection: Call) => Promise<boolean>;
  toggleMute: (connection: Call, mute: boolean) => boolean;
}

export interface CallContextType {
  currentActiveCall: QueuedCall | null;
  currentlyHeldCall: QueuedCall | null;
  isMuted: boolean;
  token?: string;
  tokenExpiry: number;
  microphoneAccessFailed: boolean;
  isAcceptingCall: boolean;
  isResumingCall: boolean;
  isConnecting: boolean;
  device: Device | null;
  deviceStatus: DeviceStatus;
  deviceError: Error | null;
  isMicrophoneEnabled: boolean;
  callStatus: CallStatus;

  // External Data
  queueStatus: GetQueueStatusResponse | undefined;
  heldCalls: GetAssetHeldCallsResponse | undefined;


  // Functions exposed by context
  acceptNextCall: () => Promise<void>;
  holdCurrentCall: () => Promise<boolean>;
  resumeCurrentCall: () => Promise<boolean>;
  resumeSpecificCall: (callToResume: QueuedCall) => Promise<boolean>;
  endCurrentCall: () => Promise<boolean>;
  muteCurrentCall: () => boolean;
  setCurrentlyHeldCall: React.Dispatch<React.SetStateAction<QueuedCall | null>>;
  makeOutboundCall: (phoneNumber: string) => Promise<boolean>;
  checkAndAddHeldCallForSituation: (situationId: string) => void;
  dequeueSpecificCall: (callSid: string) => Promise<boolean>;
}

// Create the CallContext with undefined as the default value
const CallContext = createContext<CallContextType | undefined>(undefined);

// -----------------------------------------------------------------------------
// Helper: Common Query Invalidation
// -----------------------------------------------------------------------------
const useInvalidateCommonQueries = () => {
  const queryClient = useQueryClient();
  return () => {
    queryClient.invalidateQueries({ queryKey: ['queueStatus'] });
    queryClient.invalidateQueries({ queryKey: ['heldCalls'] });
    queryClient.invalidateQueries({ queryKey: ['situations'] });
  };
};

// -----------------------------------------------------------------------------
// Provider Component
// -----------------------------------------------------------------------------
export const CallProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  // Dispatcher Asset & Asset ID
  const { asset: dispatcherAsset } = useDispatcher();
  const assetId = dispatcherAsset?.id;
  logInfo("Init", "Dispatcher asset loaded", { assetId });

  // Metrics tracking - initialize first so it's available for other hooks
  const { trackSentryCallOperation, trackSentryDeviceHealth, trackSentryStateConsistency, trackSentryPerformance } = useCallMetrics();

  // Use custom hooks for token and device management
  const { token, tokenExpiry, refreshToken, sessionSuffixRef } = useTokenManagement(assetId);
  const {
    device,
    deviceStatus,
    deviceError,
    isMicrophoneEnabled,
    microphoneAccessFailed,
    setDevice,
    setDeviceStatus,
  } = useDeviceManagement(token, assetId, refreshToken, trackSentryDeviceHealth);

  // Invalidate queries helper
  const invalidateCommonQueries = useInvalidateCommonQueries();

  // ---------------------------------------------------------------------------
  // Call State Variables
  // ---------------------------------------------------------------------------
  const [currentActiveCall, setCurrentActiveCall] = useState<QueuedCall | null>(null);
  const [currentlyHeldCall, setCurrentlyHeldCall] = useState<QueuedCall | null>(null);
  const [isMuted, setIsMuted] = useState(false);
  const [isAcceptingCall, setIsAcceptingCall] = useState(false);
  const [isResumingCall, setIsResumingCall] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [callStatus, setCallStatus] = useState<CallStatus>('idle');

  // Store timeout ID for dequeue operations
  const dequeueTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // UI cleanup helper for premature disconnects - this is not yet used
  const handleCallDisconnectedUIPart = useCallback((callSid: string) => {
    logInfo("UIUpdate:handleCallDisconnectedUIPart", `UI cleanup for partial/failed connection for call SID: ${callSid}`);
    // Reset any specific UI indicators related to connecting to this callSid
    setIsAcceptingCall(false);
    setIsConnecting(false);
    // Potentially clear other states if needed for UI indicators
  }, []);

  // Handler for call connected successfully
  const handleCallConnected = useCallback(
    (callSid: string, queueCall: QueuedCall) => {
      logInfo(
        'UIUpdate:handleCallConnected',
        'Handling connected call UI updates',
        {
          callSid,
        },
      );
      setCurrentActiveCall(queueCall);
      setIsAcceptingCall(false);
      setIsConnecting(false);
      intendedCustomerCallDataRef.current = null;
      invalidateCommonQueries();
    },
    [invalidateCommonQueries],
  );

  // Handler for call connection failures
  const handleCallConnectionFailed = useCallback(
    (callSid: string, errorMessage: string) => {
      logInfo(
        'UIUpdate:handleCallConnectionFailed',
        'Handling call connection failure UI updates',
        {
          callSid,
          errorMessage,
        },
      );
      setIsAcceptingCall(false);
      setIsConnecting(false);
      intendedCustomerCallDataRef.current = null;
      invalidateCommonQueries();
      // Use console error instead of UI alert for now
      console.error(`Connection Failed: ${errorMessage}`);
    },
    [invalidateCommonQueries],
  );

  /**
   * Handles an incoming Twilio call that matches a previously dequeued call.
   * This function is called by the central incoming call dispatcher when it 
   * receives a call with parameters that match a call we've previously dequeued.
   * 
   * The function:
   * 1. Retrieves call data from recentlyDequeuedCallDataRef that was stored during dequeueSpecificCall
   * 2. Sets up event handlers for the connection (accept, disconnect, error, etc.)
   * 3. Disables audio feedback as needed
   * 4. Accepts the call automatically
   * 5. Updates UI state to reflect the connected call
   * 
   * @param connection The Twilio Call object representing the incoming connection
   * @param customerCallSid The SID of the customer call matched from the connection parameters
   */
  // Handler for incoming calls that match a dequeued call
  const handleMatchedDequeuedCall = useCallback(
    (connection: Call, customerCallSid: string) => {
      logInfo("DeviceEvent:incoming:handleMatchedDequeuedCall", `Processing matched dequeued call for customer: ${customerCallSid}`);
      
      // Update UI state to show we're accepting the call
      setIsConnecting(false);
      setIsAcceptingCall(true);
      
      // Store the agent leg connection for later reference
      activeAgentLegInstanceRef.current[customerCallSid] = connection;
      
      // Set up event handlers for the connection
      
      // Handler: Call is accepted by the agent
      connection.on('accept', () => {
        logInfo("DeviceEvent:incoming:connection:ACCEPT", `Agent leg for ${customerCallSid} accepted.`);
        setIsAcceptingCall(false);
        
        // Track successful dequeue connection
        trackSentryCallOperation({
          callSid: customerCallSid,
          assetId: assetId || 'unknown',
          operation: 'dequeue_specific_success'
        });
        
        // Get associated call data that was stored during dequeueSpecificCall
        const associatedCallData = recentlyDequeuedCallDataRef.current?.[customerCallSid];
        if (!associatedCallData) {
          logError("DeviceEvent:incoming:connection:ACCEPT_NO_DATA", `No associated call data for ${customerCallSid}. UI might be incomplete.`);
          
          // Track missing call data issue
          trackSentryStateConsistency({
            operation: 'dequeue_connection_accept',
            callSid: customerCallSid,
            inconsistencyType: 'missing_call_data',
            autoFixed: false
          });
        }
        
        // Create a QueuedCall object for UI representation
        const newCallForUI: QueuedCall = {
          $typeName: 'hero.conversation.v1.QueuedCall',
          callSid: customerCallSid,
          caller: associatedCallData?.caller || '',
          callerName: associatedCallData?.callerName || '',
          attributes: {},
          enqueueTime: undefined,
          history: [],
          priority: 0,
          assetId: assetId || '',
          situationId: associatedCallData?.situationId || '',
          notes: '',
          callStartTime: { $typeName: 'google.protobuf.Timestamp', seconds: BigInt(Math.floor(Date.now() / 1000)), nanos: 0 }
        };
        
        // Use the existing handler to update UI state and perform common tasks
        handleCallConnected(customerCallSid, newCallForUI);
        
        // Clean up references that are no longer needed
        intendedCustomerCallDataRef.current = null;
        if (recentlyDequeuedCallDataRef.current?.[customerCallSid]) {
          delete recentlyDequeuedCallDataRef.current[customerCallSid];
        }
        
        // Clear the dequeue timeout since the call connected successfully
        if (dequeueTimeoutRef.current) {
          clearTimeout(dequeueTimeoutRef.current);
          dequeueTimeoutRef.current = null;
        }
      });
      
      // Handler: Call is disconnected
      connection.on('disconnect', () => {
        logInfo("DeviceEvent:incoming:connection:DISCONNECT", `Agent leg for ${customerCallSid} disconnected.`);
        
        // Clean up UI and core call state
        setCurrentActiveCall(null);
        setIsMuted(false);
        
        // Reset connection attempt flags
        setIsAcceptingCall(false);
        setIsConnecting(false);
        
        // Clean up references
        if (activeAgentLegInstanceRef.current[customerCallSid]) {
          delete activeAgentLegInstanceRef.current[customerCallSid];
        }
        if (intendedCustomerCallDataRef.current === customerCallSid) {
           intendedCustomerCallDataRef.current = null;
        }
        if (recentlyDequeuedCallDataRef.current?.[customerCallSid]) {
          delete recentlyDequeuedCallDataRef.current[customerCallSid];
        }
        
        invalidateCommonQueries();
      });
      
      // Handler: Connection error
      connection.on('error', (twilioError) => {
        const errorMessage = twilioError.message || 'Unknown connection error';
        const errorCode = twilioError.code;
        
        logError("DeviceEvent:incoming:connection:ERROR", `Error on agent leg for ${customerCallSid}. Code: ${errorCode}, Msg: ${errorMessage}`, { twilioError });
        
        // Use the existing handler for failed connections
        handleCallConnectionFailed(customerCallSid, errorMessage);
        
        // Clean up connection if it exists
        if (activeAgentLegInstanceRef.current[customerCallSid]) {
          try { 
            activeAgentLegInstanceRef.current[customerCallSid].disconnect(); 
          } catch (e) { 
            // Ignore disconnect errors during cleanup 
          }
          delete activeAgentLegInstanceRef.current[customerCallSid];
        }
        
        // Clean up references
        if (intendedCustomerCallDataRef.current === customerCallSid) {
           intendedCustomerCallDataRef.current = null;
        }
        if (recentlyDequeuedCallDataRef.current?.[customerCallSid]) {
          delete recentlyDequeuedCallDataRef.current[customerCallSid];
        }
        
        setIsAcceptingCall(false);
        setIsConnecting(false);
      });
      
      // Handler: Call is ringing
      connection.on('ringing', () => {
        logInfo("DeviceEvent:incoming:connection:RINGING", `Agent leg for ${customerCallSid} is ringing.`);
      });
      
      // Handler: Call is canceled by remote end
      connection.on('cancel', () => {
        logInfo("DeviceEvent:incoming:connection:CANCEL", `Agent leg for ${customerCallSid} canceled by remote end.`);
        
        // Clean up UI and core call state
        setCurrentActiveCall(null);
        setIsMuted(false);

        // Reset connection attempt flags
        setIsAcceptingCall(false);
        setIsConnecting(false);
        
        // Clean up references
        delete activeAgentLegInstanceRef.current[customerCallSid];
        if (intendedCustomerCallDataRef.current === customerCallSid) {
          intendedCustomerCallDataRef.current = null;
        }
        if (recentlyDequeuedCallDataRef.current?.[customerCallSid]) {
          delete recentlyDequeuedCallDataRef.current[customerCallSid];
        }
        
        invalidateCommonQueries();
      });
      
      // Handler: Call is rejected
      connection.on('reject', () => {
        logInfo("DeviceEvent:incoming:connection:REJECT", `Agent leg for ${customerCallSid} was rejected.`);
        
        // Clean up UI
        handleCallDisconnectedUIPart(customerCallSid);
        
        // Clean up references
        delete activeAgentLegInstanceRef.current[customerCallSid];
        if (intendedCustomerCallDataRef.current === customerCallSid) {
          intendedCustomerCallDataRef.current = null;
        }
        if (recentlyDequeuedCallDataRef.current?.[customerCallSid]) {
          delete recentlyDequeuedCallDataRef.current[customerCallSid];
        }
        
        setIsAcceptingCall(false);
        setIsConnecting(false);
      });
      
      // Disable SDK-generated sounds
      if (device?.audio) {
        device.audio.outgoing(false);
        device.audio.incoming(false);
      }
      
      // Accept the incoming call
      logInfo("DeviceEvent:incoming:ACCEPT_CALL", `Attempting to accept incoming call for ${customerCallSid}.`);
      try {
        connection.accept();
        logInfo("DeviceEvent:incoming:ACCEPT_CALLED_SUCCESS", `connection.accept() called for ${customerCallSid}.`);
      } catch (acceptError) {
        const error = acceptError as Error;
        logError("DeviceEvent:incoming:ACCEPT_CALL_ERROR", `Error calling connection.accept() for ${customerCallSid}.`, { acceptError });
        handleCallConnectionFailed(customerCallSid, error.message || 'Failed to accept call');
      }
    },
    [
      setIsConnecting, 
      setIsAcceptingCall, 
      handleCallConnected, 
      handleCallDisconnectedUIPart, 
      handleCallConnectionFailed,
      assetId,
      device
    ]);

  // Update call status based on active/held call and mute state
  useEffect(() => {
    if (currentActiveCall) {
      setCallStatus(isMuted ? 'muted' : 'active');
    } else if (currentlyHeldCall) {
      setCallStatus('held');
    } else if (isConnecting) {
      setCallStatus('connecting');
    } else {
      setCallStatus('idle');
    }
  }, [currentActiveCall, currentlyHeldCall, isMuted, isConnecting]);

  // State consistency validation
  useEffect(() => {
    // Check for active call without corresponding agent leg reference
    if (currentActiveCall && !activeAgentLegInstanceRef.current[currentActiveCall.callSid]) {
      trackSentryStateConsistency({
        operation: 'state_validation',
        callSid: currentActiveCall.callSid,
        inconsistencyType: 'active_call_missing_agent_leg',
        expectedState: `Agent leg for ${currentActiveCall.callSid}`,
        actualState: 'Missing agent leg reference',
        autoFixed: false
      });
    }

    // Check for orphaned agent leg references
    const activeCallSids = new Set([
      currentActiveCall?.callSid,
      currentlyHeldCall?.callSid
    ].filter(Boolean));
    
    for (const [sidKey, connection] of Object.entries(activeAgentLegInstanceRef.current)) {
      if (!activeCallSids.has(sidKey)) {
        trackSentryStateConsistency({
          operation: 'state_validation',
          callSid: sidKey,
          inconsistencyType: 'orphaned_agent_leg_reference',
          expectedState: 'No agent leg reference',
          actualState: `Orphaned reference for ${sidKey}`,
          autoFixed: false
        });
      }
    }

    // Check for device/call state mismatches
    if (currentActiveCall && deviceStatus !== 'ready') {
      trackSentryStateConsistency({
        operation: 'state_validation',
        callSid: currentActiveCall.callSid,
        inconsistencyType: 'active_call_device_not_ready',
        expectedState: 'Device ready',
        actualState: `Device status: ${deviceStatus}`,
        autoFixed: false
      });
    }
  }, [currentActiveCall, currentlyHeldCall, deviceStatus, trackSentryStateConsistency]);

  /**
   * Central Incoming Call Dispatcher
   * 
   * This useEffect sets up a centralized dispatcher for all incoming Twilio calls.
   * It analyzes the call parameters to determine the call type and routes each call
   * to the appropriate handler:
   * 
   * 1. Dequeued calls: Matches the customerCallSid parameter against intendedCustomerCallDataRef
   *    and routes to handleMatchedDequeuedCall if there's a match
   *    - Uses robust parameter extraction to handle various formats:
   *      - Direct parameters (params.customerCallSid)
   *      - Nested parameters (params.params object)
   *      - URL-style query strings (params.params = "customerCallSid=value")
   *      - Case variations (CustomerCallSid, customercallsid)
   * 
   * 2. Resume calls: Identifies calls with the resume=true parameter and handles them as
   *    calls being resumed from hold
   * 
   * 3. Unrecognized calls: Automatically rejects any calls that don't match known patterns
   *    to prevent unexpected connections
   * 
   * This central dispatcher approach ensures consistent call handling, proper logging,
   * and reliable cleanup of resources regardless of how the call was initiated.
   */
  // Handle incoming calls for dequeued call handling
  useEffect(() => {
    if (!device) return;
    
    // Set up centralized dispatcher for all incoming calls
    const handleIncoming = (incomingCall: Call) => {
      // Log raw parameters immediately upon receiving the call
      console.log(
        `[CallContext][DeviceEvent][RAW_INCOMING_PARAMETERS_ON_EVENT] ${new Date().toISOString()}`,
        JSON.stringify(incomingCall.parameters, null, 2)
      );

      // Get all possible parameters from the call
      const params = incomingCall.parameters || {};
      
      // Safely parse nested params if they exist
      let rawParams: Record<string, any> = {};
      const nestedParamSource = params.params || params.Params; // Check both 'params' and 'Params'
      
      if (nestedParamSource) {
        try {
          if (typeof nestedParamSource === 'string') {
            // Try JSON first
            try {
              rawParams = JSON.parse(nestedParamSource);
            } catch {
              // If not JSON, try URL-style parameters (key=value&key2=value2)
              if (nestedParamSource.includes('=')) {
                const urlParams = nestedParamSource.split('&');
                urlParams.forEach(param => {
                  const [key, value] = param.split('=');
                  if (key && value) {
                    rawParams[key] = value;
                  }
                });
              }
            }
          } else if (typeof nestedParamSource === 'object' && nestedParamSource !== null) {
            // It's already an object
            rawParams = nestedParamSource;
          }
        } catch (e) {
          logError("DeviceEvent:incoming:parseError", "Failed to parse nested params", { 
            paramsValue: nestedParamSource,
            error: e
          });
        }
      }
      
      // Try to find customerCallSid in multiple possible locations
      const customerCallSid = params.customerCallSid || 
                             rawParams.customerCallSid || 
                             params.CustomerCallSid ||
                             params.customercallsid;
      
      // Log detailed parameter info for debugging
      logInfo("DeviceEvent:incoming", "Central call dispatcher: Incoming call received", { 
        parameters: params,
        rawParams: rawParams,
        extractedCustomerCallSid: customerCallSid,
        intendedCallSid: intendedCustomerCallDataRef.current
      });
      
      // CASE 1: Call is a matched dequeued call (from dequeueCallBySid)
      if (customerCallSid && intendedCustomerCallDataRef.current === customerCallSid) {
        logInfo("DeviceEvent:incoming:match", "Dispatcher: Handling matched dequeued call", { customerCallSid });
        handleMatchedDequeuedCall(incomingCall, customerCallSid);
        return;
      }
      
      // CASE 2: Call is a standard dequeue call (from acceptNextCall)
      if (params.action === 'dequeue' && !customerCallSid) {
        logInfo("DeviceEvent:incoming:dequeue", "Dispatcher: Standard dequeued call", { params });
        // Let standard connection handlers manage this call
        return;
      }
      
      // CASE 3: Call is unexpected or unhandled
      logInfo("DeviceEvent:incoming:unhandled", "Dispatcher: Rejecting unhandled incoming call", { 
        callParams: params,
        rawParams: rawParams,
        intendedCallSid: intendedCustomerCallDataRef.current,
        customerCallSid: customerCallSid
      });
      
      // Explicitly reject unhandled calls to prevent hanging connections
      try {
        incomingCall.reject();
        logInfo("DeviceEvent:incoming:rejected", "Dispatcher: Successfully rejected unhandled call");
      } catch (rejectError) {
        logError("DeviceEvent:incoming:reject_error", "Dispatcher: Error rejecting unhandled call", { rejectError });
      }
    };
    
    // Register the event handler
    device.on('incoming', handleIncoming);
    
    // Clean up handler when device changes or component unmounts
    return () => {
      device.off('incoming', handleIncoming);
    };
  }, [device, handleMatchedDequeuedCall]);

  // ---------------------------------------------------------------------------
  // External Data: Querying Queue Status and Held Calls
  // ---------------------------------------------------------------------------
  const { data: queueStatus } = useQuery<GetQueueStatusResponse, Error>({
    queryKey: ['queueStatus'],
    queryFn: () => getQueueStatus({} as GetQueueStatusRequest),
    refetchInterval: 5000, 
  });

  const { data: heldCalls, refetch: refetchHeldCalls } = useQuery<GetAssetHeldCallsResponse, Error>({
    queryKey: ['heldCalls', assetId],
    queryFn: () => getAssetHeldCalls({ assetId } as GetAssetHeldCallsRequest),
    refetchInterval: HELD_CALLS_REFRESH_INTERVAL,
    enabled: !!assetId,
  });

  useEffect(() => {
    if (heldCalls) {
      if (
        currentlyHeldCall &&
        !heldCalls?.heldCalls?.some((call) => call.callSid === currentlyHeldCall.callSid)
      ) {
        setCurrentlyHeldCall(null);
      }
      logInfo("HeldCalls", "Held calls data updated", { heldCalls });
    }
  }, [heldCalls]);


  // ---------------------------------------------------------------------------
  // Call Operations & Handlers
  // ---------------------------------------------------------------------------
  /**
   * SID Tracking System Reference Guide:
   * 
   * Twilio uses different SIDs for different legs of each call:
   * 
   * For Outbound Calls:
   * - Initial temp SID (client-side only) = `outbound-${timestamp}`
   * - Real SID (after accept) = connection.parameters.CallSid (an agent leg SID, CA...)
   * 
   * For Inbound Calls via acceptNextCall():
   * - Single SID = callData.callSid (a customer/parent SID, CA...)
   * 
   * For Inbound Calls via dequeueSpecificCall():
   * - Customer SID tracked in intendedCustomerCallDataRef (parent call CA...)
   * - Agent leg SID available in connection.parameters.CallSid (CA...)
   * 
   * Key Call Flows:
   * 1. Outbound: makeOutboundCall → setupConnectionHandlers → connection.accept → change temp SID to real SID
   * 2. Inbound (Next): acceptNextCall → dequeueAndConnectCallAPI → setupConnectionHandlers
   * 3. Inbound (Specific): dequeueSpecificCall → intendedCustomerCallDataRef → handleMatchedDequeuedCall <- currently this is active
   */

  // Stores active Twilio agent leg connections, indexed by call SID
  // IMPORTANT: For outbound calls, initially keyed by temp SID, but should be updated to real agent leg SID after accept
  // For inbound calls, keyed by customer/parent SID
  const activeAgentLegInstanceRef = useRef<Record<string, Call>>({});
  
  // Stores the callSid of the customer call that the agent intends to connect to
  // Only used in dequeueSpecificCall() flow for selective call pickup
  // This is used by the incoming call handler to know which specific call to accept
  const intendedCustomerCallDataRef = useRef<string | null>(null);
  
  // Stores the backend response data for dequeued calls, indexed by callSid
  // Used to retrieve caller name, number and other metadata when a matching incoming call arrives
  // This reference is cleared after the call is connected or fails
  const recentlyDequeuedCallDataRef = useRef<Record<string, DequeueCallBySidResponse>>({});

  // Setup common connection event handlers
  const setupConnectionHandlers = useCallback((connection: Call, callToActivate: QueuedCall): void => {
    if (!connection) return;
    
    connection.on('accept', () => {
      const agentLegSid = connection.parameters.CallSid; // SID of the agent's leg of the call (CA...)
      const initialCallToActivateSid = callToActivate.callSid; // For inbound: CA... (customer) | For outbound: outbound-... (temporary)
      const isOutboundCall = callToActivate.attributes?.direction === 'outbound';

      logInfo("ConnectionHandler", "Call accepted by SDK", {
        agentLegSid,
        initialCallToActivateSid,
        isOutboundCall,
        direction: callToActivate.attributes?.direction,
      });

      // Track outbound call connection success
      if (isOutboundCall) {
        trackSentryCallOperation({
          callSid: agentLegSid, // Use the real agent leg SID for outbound calls
          assetId: assetId || 'unknown',
          operation: 'outbound_call_connected',
          duration: undefined // We don't have the full duration here, tracked in makeOutboundCall
        });
      }

      // Determine the SID to be used for currentActiveCall state and as the key for activeAgentLegInstanceRef
      // For outbound calls, this should be the agentLegSid.
      // For inbound calls, this should be the customer's original SID.
      const finalSidForCallState = isOutboundCall ? agentLegSid : initialCallToActivateSid;

      const updatedCallToActivate: QueuedCall = {
        ...callToActivate,
        callSid: finalSidForCallState,
      };

      // Manage activeAgentLegInstanceRef
      // If it's an outbound call and the initial temporary SID is different from the agentLegSid,
      // remove the entry keyed by the temporary SID.
      if (isOutboundCall && initialCallToActivateSid !== agentLegSid && activeAgentLegInstanceRef.current[initialCallToActivateSid]) {
        logInfo("ConnectionHandler", "Removing temporary outbound SID from activeAgentLegInstanceRef", { tempSid: initialCallToActivateSid });
        delete activeAgentLegInstanceRef.current[initialCallToActivateSid];
      }
      // Store the connection, keyed by the SID that will be in currentActiveCall.callSid
      activeAgentLegInstanceRef.current[finalSidForCallState] = connection;

      setCurrentActiveCall(updatedCallToActivate);
      
      // Clear connection attempt flags
      setIsAcceptingCall(false); 
      setIsConnecting(false);   
      
      // Clear any intended call SID now that this one is active
      if (intendedCustomerCallDataRef.current === finalSidForCallState || intendedCustomerCallDataRef.current === initialCallToActivateSid) {
          intendedCustomerCallDataRef.current = null;
      }
      // Clean up recently dequeued data if it matches this call
      if (recentlyDequeuedCallDataRef.current?.[finalSidForCallState]) {
          delete recentlyDequeuedCallDataRef.current[finalSidForCallState];
      } else if (recentlyDequeuedCallDataRef.current?.[initialCallToActivateSid]) {
          delete recentlyDequeuedCallDataRef.current[initialCallToActivateSid];
      }

      invalidateCommonQueries();
    });

    connection.on('disconnect', () => {
      const agentLegSid = connection.parameters.CallSid;
      const isOutboundCall = callToActivate.attributes?.direction === 'outbound';
      
      logInfo("ConnectionHandler", "Call disconnected", { 
        connection, 
        callSid: callToActivate.callSid,
        agentLegSid: agentLegSid,
        currentActiveCallSid: currentActiveCall?.callSid,
        isOutboundCall
      });

      // Track outbound call disconnection
      if (isOutboundCall) {
        trackSentryCallOperation({
          callSid: agentLegSid || callToActivate.callSid,
          assetId: assetId || 'unknown',
          operation: 'outbound_call_disconnected'
        });
      }
      
      setCurrentActiveCall(null);
      setIsMuted(false);
      setIsAcceptingCall(false);
      setIsConnecting(false);

      // Clean up both possible references
      if (activeAgentLegInstanceRef.current[callToActivate.callSid]) {
        delete activeAgentLegInstanceRef.current[callToActivate.callSid];
      }
      if (agentLegSid && activeAgentLegInstanceRef.current[agentLegSid]) {
        delete activeAgentLegInstanceRef.current[agentLegSid];
      }
      
      // Clean up other refs
      if (intendedCustomerCallDataRef.current === callToActivate.callSid ||
          (agentLegSid && intendedCustomerCallDataRef.current === agentLegSid)) {
         intendedCustomerCallDataRef.current = null;
      }
      
      // Clean up dequeued data
      if (recentlyDequeuedCallDataRef.current?.[callToActivate.callSid]) {
        delete recentlyDequeuedCallDataRef.current[callToActivate.callSid];
      }
      if (agentLegSid && recentlyDequeuedCallDataRef.current?.[agentLegSid]) {
        delete recentlyDequeuedCallDataRef.current[agentLegSid];
      }

      // Set device to ready
      if (device && (deviceStatus === 'registering' || deviceStatus === 'connecting' || deviceStatus === 'ready') && !deviceError) { 
        setDeviceStatus('ready');
      }
      
      invalidateCommonQueries();
    });

    connection.on('error', (error: Error) => {
      const isOutboundCall = callToActivate.attributes?.direction === 'outbound';
      
      logError("ConnectionHandler", "Error in connection for call", { error, callSid: callToActivate.callSid, isOutboundCall });

      // Track outbound call errors
      if (isOutboundCall) {
        trackSentryCallOperation({
          callSid: callToActivate.callSid,
          assetId: assetId || 'unknown',
          operation: 'outbound_call_failed',
          errorType: 'connection_error',
          errorMessage: error.message,
          stage: 'active_connection'
        });
      }
      
      if (currentActiveCall?.callSid === callToActivate.callSid || activeAgentLegInstanceRef.current[callToActivate.callSid] === connection) {
          setCurrentActiveCall(null);
          setIsMuted(false);
      }
      
      handleCallConnectionFailed(callToActivate.callSid, error.message);
      
      if (activeAgentLegInstanceRef.current[callToActivate.callSid]) {
        try { 
          activeAgentLegInstanceRef.current[callToActivate.callSid].disconnect(); 
        } catch (e) { 
          // Ignore disconnect errors during cleanup 
        }
        delete activeAgentLegInstanceRef.current[callToActivate.callSid];
      }
      
      if (recentlyDequeuedCallDataRef.current?.[callToActivate.callSid]) {
        delete recentlyDequeuedCallDataRef.current[callToActivate.callSid];
      }

       // handleCallConnectionFailed already sets isAcceptingCall and isConnecting to false
       // and clears intendedCustomerCallDataRef.
       invalidateCommonQueries();
    });

    connection.on('cancel', () => {
      logInfo("ConnectionHandler", "Call canceled for", { connection, callSid: callToActivate.callSid });
      
      // Similar comprehensive cleanup as 'disconnect'
      if (currentActiveCall?.callSid === callToActivate.callSid || activeAgentLegInstanceRef.current[callToActivate.callSid] === connection) {
        setCurrentActiveCall(null);
        setIsMuted(false);
      }
      
      setIsAcceptingCall(false);
      setIsConnecting(false);
      
      if (activeAgentLegInstanceRef.current[callToActivate.callSid]) {
        delete activeAgentLegInstanceRef.current[callToActivate.callSid];
      }
      if (intendedCustomerCallDataRef.current === callToActivate.callSid) {
         intendedCustomerCallDataRef.current = null;
      }
      if (recentlyDequeuedCallDataRef.current?.[callToActivate.callSid]) {
        delete recentlyDequeuedCallDataRef.current[callToActivate.callSid];
      }
      
      invalidateCommonQueries();
    });

    connection.on('reject', () => {
      logInfo("ConnectionHandler", "Call rejected for", { connection, callSid: callToActivate.callSid });
      if (currentActiveCall?.callSid === callToActivate.callSid) {
        // setCurrentActiveCall(null); // Or handleCallDisconnectedUIPart
      }
      // Use a more general cleanup if a call attempt is rejected.
      handleCallDisconnectedUIPart(callToActivate.callSid); // This sets isAcceptingCall and isConnecting to false
      invalidateCommonQueries();
    });

    connection.on('ringing', () => {
      logInfo("ConnectionHandler", "Call ringing for", { connection, callSid: callToActivate.callSid });
      setCallStatus('connecting');
    });
  }, [
    setCurrentActiveCall, setIsMuted, setDeviceStatus, invalidateCommonQueries, setCallStatus, 
    currentActiveCall, device, deviceStatus, deviceError, 
    setIsAcceptingCall, setIsConnecting, intendedCustomerCallDataRef, recentlyDequeuedCallDataRef, handleCallDisconnectedUIPart
  ]);

  // Dequeue and connect a call (currently not used, but kept for future reference, Need to clean up for future ref)
  const dequeueAndConnectCallAPI = useCallback(async () => {
    if (!device || !assetId) {
      throw new Error('Device or asset ID not available');
    }
    setIsConnecting(true); // Set connecting state early

    try {
      logInfo("dequeueAndConnectCallAPI", "Attempting to dequeue call", { assetId });
      const backendCallData: DequeueCallResponse = await dequeueCall({ assetId } as DequeueCallRequest);
      
      if (!backendCallData.success) {
        // DequeueCallResponse does not have a generic 'message' field.
        // We rely on the 'success' flag and can provide a generic error or check for specific conditions.
        const errorMsg = "Dequeue operation failed."; // Generic error
        if (backendCallData.callSid && backendCallData.caller) { // Basic check if some data came back despite !success
             // Potentially log more specific details if available, but stick to a generic message for the throw
             logInfo("dequeueAndConnectCallAPI", "Dequeue reported as not successful but some data present", { backendCallData });
        } else {
            logInfo("dequeueAndConnectCallAPI", "Dequeue reported as not successful. No calls likely available.");
            // This specific error message is handled by acceptNextCall to provide a user-friendly alert.
            throw new Error('No calls available in queue'); 
        }
        throw new Error(errorMsg); // Fallback generic error
      }

      const queueName = backendCallData.queueName;
      const connection: Call = await device.connect({
        params: { action: 'dequeue', queue: queueName },
      });

      if (device?.audio) {
        device.audio.outgoing(false);
        device.audio.incoming(false);
      }

      activeAgentLegInstanceRef.current[backendCallData.callSid] = connection;

      const newCallToActivate: QueuedCall = {
        $typeName: 'hero.conversation.v1.QueuedCall',
        callSid: backendCallData.callSid,
        caller: backendCallData.caller,
        callerName: backendCallData.callerName,
        attributes: backendCallData.attributes,
        enqueueTime: undefined, // DequeueCallResponse doesn't provide this; set to undefined as before
        history: [],
        priority: 0,
        assetId: assetId,
        situationId: backendCallData.situationId,
        notes: '',
        callStartTime: backendCallData.callStartTime,
      };
      
      setupConnectionHandlers(connection, newCallToActivate); 
      
      logInfo("dequeueAndConnectCallAPI", "Call connection initiated, waiting for 'accept' event", { agentLegSid: connection.parameters.CallSid, customerCallSid: backendCallData.callSid });
      
      return { connection, callDataForUI: newCallToActivate };
    } catch (error) {
      logError("dequeueAndConnectCallAPI", "Error during call initiation", { error });
      // Ensure flags are reset if an error occurs before handlers are set up or take over
      setIsAcceptingCall(false); 
      setIsConnecting(false);
      throw error;
    }
  }, [device, assetId, setupConnectionHandlers, setIsAcceptingCall, setIsConnecting]);

  // Place a call on hold
  const placeCallOnHoldAPI = useCallback(
    async (callSid: string, connection: Call) => {
      if (!assetId) {
        throw new Error('Asset ID not available');
      }
      try {
        activeAgentLegInstanceRef.current[callSid] = connection;
        logInfo("placeCallOnHoldAPI", "Placing call on hold", { callSid, assetId });
        
        // Disable disconnect sound BEFORE backend call
        if (device?.audio) {
          device.audio.disconnect(false);
        }
        
        const holdResult = await holdCall({
          callSid,
          assetId,
          reason: 'Call placed on hold by agent',
        } as HoldCallRequest);
        if (!holdResult.success) {
          throw new Error('Backend failed to place call on hold');
        }
        
        connection.disconnect();
        setDeviceStatus('ready');
        setCurrentActiveCall(null);
        logInfo("placeCallOnHoldAPI", "Call placed on hold successfully", { callSid });
        return true;
      } catch (error) {
        logError("placeCallOnHoldAPI", "Error placing call on hold", { error });
        throw error;
      }
    },
    [assetId, setDeviceStatus]
  );

  // Resume a call via the backend API
  const resumeCallAPI = useCallback(async (callSid: string): Promise<{ success: boolean }> => {
    if (!device || !assetId) {
      throw new Error("Device or asset ID not available");
    }
    logInfo("resumeCallAPI", "Resuming call", { callSid });
    setIsResumingCall(true);
    try {
      if (device) {
        const handleOnceIncoming = (conn: Call) => {
          logInfo("resumeCallAPI", "Incoming call detected during resume", { connection: conn });
          conn.accept();
          activeAgentLegInstanceRef.current[callSid] = conn;
          setIsResumingCall(false);
          conn.on('disconnect', () => {
            setCurrentActiveCall(null);
          });
        };
        device.once('incoming', handleOnceIncoming);
        setTimeout(() => {
          device.removeListener('incoming', handleOnceIncoming);
          setIsResumingCall(false);
        }, 10000);
      }
      logInfo("resumeCallAPI", "Resume initiated, waiting for incoming connection", { callSid });
      await resumeCall({
        callSid,
        assetId,
        currentSessionSuffix: sessionSuffixRef.current
      } as ResumeCallRequest);
      return { success: true };
    } catch (error) {
      logError("resumeCallAPI", "Failed to resume call", { error });
      setIsResumingCall(false);
      throw error;
    }
  }, [device, assetId]);

  // End a call via the backend API
  const endCallAPI = useCallback(
    async (callSidToTerminate: string, connection: Call | null) => { // SID received is now assumed to be the one for backend
      if (!assetId) throw new Error('Asset ID not available');

      logInfo("endCallAPI", "Attempting to end call with backend using SID:", { sidToUseForBackend: callSidToTerminate });

      try {
        if (device?.audio) {
          device.audio.disconnect(false); // Still good practice
        }
        
        await endCall({
          callSid: callSidToTerminate, 
          assetId,
          reason: 'Call ended by agent',
        } as EndCallRequest);

        if (connection) {
          try {
            connection.disconnect();
          } catch (disconnectError) {
            logInfo("endCallAPI", "Error during local SDK disconnect, likely already disconnected.", { disconnectError });
          }
        }
        
        logInfo("endCallAPI", "Call ended successfully via backend and SDK leg disconnected.", { endedSid: callSidToTerminate });
        return true;
      } catch (error) {
        logError("endCallAPI", "Error ending call via backend/SDK", { error, attemptedBackendSid: callSidToTerminate });
        const err = error as Error;
        
        if (err.message?.includes('was not found') || err.message?.includes('Connection already disconnected')) {
          logInfo("endCallAPI", "Call likely already ended on Twilio or connection was already disconnected. Considered success for UI cleanup.");
          if (connection) { try { connection.disconnect(); } catch (e) { /* ignore */ } }
          return true; 
        }
        
        logError("endCallAPI", "Unhandled error in endCallAPI, will propagate.", { errMessage: err.message });
        return false; 
      }
    },
    [assetId, device]
  );

  // Toggle mute on the connection
  const toggleMute = useCallback(
    (connection: Call, mute: boolean): boolean => {
      if (!connection) return false;
      try {
        logInfo("toggleMute", "Toggling mute", { mute, connection });
        connection.mute(mute);
        setIsMuted(mute);
        return true;
      } catch (error) {
        logError("toggleMute", "Failed to toggle mute", { error });
        return false;
      }
    },
    []
  );

  // ---------------------------------------------------------------------------
  // Exposed Functions
  // ---------------------------------------------------------------------------

  //
  // These external facing functions combine backend operations with state updates
  // Philosophy: Don't call the apis directly from the UI.
  // Use the functions below exposed through context to manage the state and call the APIs.
  //

  // Make an outbound call to a phone number
  const makeOutboundCall = async (phoneNumber: string): Promise<boolean> => {
    const startTime = Date.now();
    const tempCallSid = `outbound-${Date.now()}`;
    
    // Track outbound call initiation
    trackSentryCallOperation({
      callSid: tempCallSid,
      assetId: assetId || 'unknown',
      operation: 'outbound_call_initiated',
      timestamp: startTime
    });

    // Validate prerequisites
    if (!device || !assetId || deviceStatus !== 'ready' || currentActiveCall) {
      const issues = [];
      if (!device) issues.push('no device');
      if (!assetId) issues.push('no assetId');
      if (deviceStatus !== 'ready') issues.push(`device not ready (${deviceStatus})`);
      if (currentActiveCall) issues.push('active call exists');
      
      const errorMessage = `Cannot initiate outbound call: ${issues.join(', ')}`;
      
      logInfo("makeOutboundCall", "Cannot initiate call - prerequisites not met", {
        deviceStatus,
        hasActiveCall: !!currentActiveCall,
        issues
      });
      
      // Track validation failure
      trackSentryCallOperation({
        callSid: tempCallSid,
        assetId: assetId || 'unknown',
        operation: 'outbound_call_failed',
        errorType: 'validation_failed',
        errorMessage,
        stage: 'precondition_check',
        duration: Date.now() - startTime
      });
      
      return false;
    }

    setIsAcceptingCall(true);
    
    try {
      logInfo("makeOutboundCall", "Initiating outbound call", { phoneNumber, tempCallSid });
      
      // Clean the phone number
      const cleanedNumber = phoneNumber.replace(/\D/g, '');
      
      // Track phone number validation
      if (cleanedNumber.length < 10) {
        const errorMessage = `Invalid phone number format: ${phoneNumber} -> ${cleanedNumber}`;
        trackSentryCallOperation({
          callSid: tempCallSid,
          assetId: assetId || 'unknown',
          operation: 'outbound_call_failed',
          errorType: 'invalid_phone_number',
          errorMessage,
          stage: 'phone_validation',
          duration: Date.now() - startTime
        });
        throw new Error(errorMessage);
      }
      
      // Set up call parameters
      const params = {
        params: {
          To: cleanedNumber,
          assetId: assetId,
          action: 'outbound'
        }
      };
      
      // Track Twilio connection attempt
      const connectionStartTime = Date.now();
      
      // Connect to Twilio
      const connection = await device.connect(params);
      
      const connectionDuration = Date.now() - connectionStartTime;
      
      // Track successful Twilio connection
      trackSentryCallOperation({
        callSid: tempCallSid,
        assetId: assetId || 'unknown',
        operation: 'outbound_twilio_connect_success',
        duration: connectionDuration
      });
      
      trackSentryPerformance('outbound_twilio_connect', connectionDuration, {
        phoneNumber: cleanedNumber,
        tempCallSid
      });
      
      // Create a QueuedCall object for the outbound call
      const outboundCall: QueuedCall = {
        $typeName: 'hero.conversation.v1.QueuedCall',
        callSid: tempCallSid,
        caller: phoneNumber,
        callerName: 'Outbound Call',
        enqueueTime: isoStringToProtoTimestamp(new Date().toISOString()),
        callStartTime: isoStringToProtoTimestamp(new Date().toISOString()),
        attributes: {
          direction: 'outbound',
          destination: cleanedNumber
        },
        history: [],
        priority: 0,
        assetId: assetId,
        situationId: '',
        notes: `Outbound call to ${phoneNumber}`
      };
      
      // Set up standard event handlers. Pass the outboundCall object which contains the temporary SID.
      // setupConnectionHandlers will handle replacing it with the real SID upon connection.
      setupConnectionHandlers(connection, outboundCall);
      
      // Track successful outbound call setup
      trackSentryCallOperation({
        callSid: tempCallSid,
        assetId: assetId || 'unknown',
        operation: 'outbound_call_setup_success',
        duration: Date.now() - startTime
      });
      
      return true;
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = (error as Error).message;
      
      logError("makeOutboundCall", "Failed to make outbound call", { error, phoneNumber });
      
      // Track outbound call failure
      trackSentryCallOperation({
        callSid: tempCallSid,
        assetId: assetId || 'unknown',
        operation: 'outbound_call_failed',
        errorType: 'connection_error',
        errorMessage,
        stage: 'twilio_connect',
        duration
      });
      
      throw error;
    } finally {
      setIsAcceptingCall(false);
    }
  };

  /**
   * Dequeues a specific call by its SID and prepares for an incoming Twilio connection.
   * 
   * The dequeue-by-sid flow works as follows:
   * 1. Agent selects a specific call to dequeue (from queue list)
   * 2. This function makes a gRPC call to backend to claim the call (sets it to pending_selective_assignment)
   * 3. The function stores the callSid in intendedCustomerCallDataRef for later matching
   * 4. The function stores the backend response in recentlyDequeuedCallDataRef for later use
   * 5. The backend processes the claim and instructs Twilio to make an incoming call to our device
   * 6. Our central incoming call dispatcher (in the useEffect) receives the call and:
   *    - Matches it against intendedCustomerCallDataRef
   *    - Gets the stored response data from recentlyDequeuedCallDataRef
   *    - Establishes the connection and sets up call event handlers
   *    - Updates UI to show the connected call
   * 
   * Error handling:
   * - If the backend claim fails, we show an error and clean up state
   * - If the incoming call doesn't arrive within the timeout, we:
   *   - Clean up state
   *   - Try to revert the claim (if possible)
   *   - Show an error to the user
   * - If the connection fails after it's established, the call event handlers clean up state
   * 
   * @param callSid The SID of the call to dequeue
   * @returns {Promise<boolean>} True if the dequeue process was successfully initiated
   */
  // Dequeue a specific call by SID and connect to it
  const dequeueSpecificCall = async (callSid: string) => {
    const startTime = Date.now();
    
    // Track operation initiation
    trackSentryCallOperation({
      callSid,
      assetId: assetId || 'unknown',
      operation: 'dequeue_specific_initiated',
      timestamp: startTime
    });

    // 1. Validate inputs and preconditions
    if (!assetId || !device || deviceStatus !== 'ready') {
      const errorMessage = `Device not ready or missing parameters. AssetId: ${assetId}, DeviceStatus: ${deviceStatus}`;
      logError("dequeueSpecificCall", errorMessage, { 
        assetId, deviceStatus, callSid 
      });
      
      // Track validation failure
      trackSentryCallOperation({
        callSid,
        assetId: assetId || 'unknown',
        operation: 'dequeue_specific_failed',
        errorType: 'validation_failed',
        errorMessage,
        stage: 'precondition_check',
        duration: Date.now() - startTime
      });
      
      alert('Cannot connect to call: Device not ready');
      return false;
    }
    
    // 2. Handle existing active call
    if (currentActiveCall) {
      logInfo("dequeueSpecificCall", "Active call detected; placing current call on hold", {
        currentActiveCall,
      });
      try {
        await holdCurrentCall();
        // Track successful hold operation
        trackSentryCallOperation({
          callSid: currentActiveCall.callSid,
          assetId: assetId || 'unknown',
          operation: 'hold_for_dequeue_success'
        });
      } catch (holdError) {
        const errorMessage = `Failed to place current call on hold: ${(holdError as Error).message}`;
        logError("dequeueSpecificCall", errorMessage, { holdError });
        
        // Track hold failure
        trackSentryCallOperation({
          callSid,
          assetId: assetId || 'unknown',
          operation: 'dequeue_specific_failed',
          errorType: 'hold_failed',
          errorMessage,
          stage: 'hold_current_call',
          duration: Date.now() - startTime
        });
        
        alert('Failed to place current call on hold. Please try again.');
        return false;
      }
    }
    
    // 3. Start connection process
    setIsConnecting(true);
    setIsAcceptingCall(true);
    logInfo("dequeueSpecificCall", "Attempting to dequeue specific call", { callSid, assetId });
    
    try {
      // 4. Call backend to claim the call (sets state to pending_selective_assignment)
      const backendStartTime = Date.now();
      const callData = await dequeueCallBySid({
        $typeName: 'hero.conversation.v1.DequeueCallBySidRequest',
        assetId,
        callSid,
        sessionSuffix: sessionSuffixRef.current
      });
      
      const backendDuration = Date.now() - backendStartTime;
      
      // Track backend API performance
      trackSentryPerformance('dequeue_backend_api', backendDuration, {
        callSid,
        success: callData.success
      });
      
      if (!callData.success) {
        const errorMessage = 'Failed to claim call: Backend returned success=false';
        
        // Track backend failure
        trackSentryCallOperation({
          callSid,
          assetId: assetId || 'unknown',
          operation: 'dequeue_specific_failed',
          errorType: 'backend_failed',
          errorMessage,
          stage: 'backend_claim',
          duration: Date.now() - startTime
        });
        
        throw new Error(errorMessage);
      }
      
      // Track successful backend claim
      trackSentryCallOperation({
        callSid,
        assetId: assetId || 'unknown',
        operation: 'dequeue_backend_claim_success',
        duration: backendDuration
      });
      
      logInfo("dequeueSpecificCall", "Call claimed successfully, waiting for incoming connection", { 
        callSid, callData 
      });
      
      // 5. Store the call SID for reference - this is used by the dispatcher to match the incoming call
      intendedCustomerCallDataRef.current = callSid;
      
      // 6. Store the backend response for later reference - used when building call UI on connect
      recentlyDequeuedCallDataRef.current[callSid] = callData;
      
      // 7. Rather than connecting directly, we now wait for the incoming call to be received
      // and matched by our central incoming call dispatcher in the useEffect above.
      // The dispatcher will call handleMatchedDequeuedCall when it sees an incoming call
      // with a matching customerCallSid parameter.
      
      // Set up a timeout to clean up if no matching call arrives
      const timeoutDuration = 15000; // 15 seconds
      // Clear any existing timeout first
      if (dequeueTimeoutRef.current) {
        clearTimeout(dequeueTimeoutRef.current);
      }
      dequeueTimeoutRef.current = setTimeout(() => {
        if (intendedCustomerCallDataRef.current === callSid) {
          const timeoutDuration = Date.now() - startTime;
          const errorMessage = `Connection timed out after ${timeoutDuration}ms`;
          
          logError("dequeueSpecificCall", "Timeout waiting for incoming call", { callSid });
          
          // Track timeout failure
          trackSentryCallOperation({
            callSid,
            assetId: assetId || 'unknown',
            operation: 'dequeue_specific_failed',
            errorType: 'connection_timeout',
            errorMessage,
            stage: 'awaiting_incoming_call',
            duration: timeoutDuration
          });
          
          // Clean up state
          handleCallConnectionFailed(callSid, errorMessage);
          
          // Clean up references
          intendedCustomerCallDataRef.current = null;
          if (recentlyDequeuedCallDataRef.current[callSid]) {
            delete recentlyDequeuedCallDataRef.current[callSid];
          }
          
          // Try to revert the claim to requeue the call
          try {
            revertSelectiveClaim({
              $typeName: 'hero.conversation.v1.RevertSelectiveClaimRequest',
              callSid: callSid
            }).catch(revertError => {
              logError("dequeueSpecificCall", "Failed to revert claim after timeout", { revertError });
              // Track revert failure
              trackSentryCallOperation({
                callSid,
                assetId: assetId || 'unknown',
                operation: 'revert_claim_failed',
                errorType: 'revert_after_timeout',
                errorMessage: (revertError as Error).message
              });
            });
          } catch (revertError) {
            logError("dequeueSpecificCall", "Failed to revert claim after timeout", { revertError });
          }
        }
      }, timeoutDuration);
            
      // Clean up the timeout when component unmounts
      return true;
    } catch (error) {
      // 8. Handle errors during the connection process
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const duration = Date.now() - startTime;
      
      logError("dequeueSpecificCall", "Failed to dequeue specific call", { error, callSid, errorMessage });
      
      // Track the error with full context
      trackSentryCallOperation({
        callSid,
        assetId: assetId || 'unknown',
        operation: 'dequeue_specific_failed',
        errorType: 'general_error',
        errorMessage,
        stage: 'unknown',
        duration
      });
      
      // Reset state
      setIsConnecting(false);
      setIsAcceptingCall(false);
      
      // Clean up references
      intendedCustomerCallDataRef.current = null;
      if (recentlyDequeuedCallDataRef.current[callSid]) {
        delete recentlyDequeuedCallDataRef.current[callSid];
      }
      
      // Clear the dequeue timeout since we had an error
      if (dequeueTimeoutRef.current) {
        clearTimeout(dequeueTimeoutRef.current);
        dequeueTimeoutRef.current = null;
      }
      
      // Show error to user
      alert(`Failed to connect to call: ${errorMessage}`);
      
      // Refresh data
      invalidateCommonQueries();
      return false;
    }
  };

  // Accept the next call – if a current call exists, place it on hold first
  const acceptNextCall = async () => {
    logInfo("acceptNextCall", "Asset ID and Device Status", { assetId, device, deviceStatus });
    if (!assetId || !device || deviceStatus !== 'ready') {
      alert("Device not ready or asset not available."); // User feedback
      return;
    }
    if (currentActiveCall) {
      logInfo("acceptNextCall", "Active call detected; placing current call on hold", {
        currentActiveCall,
      });
      try {
        await holdCurrentCall();
      } catch (holdError) {
        logError("acceptNextCall", "Failed to place current call on hold when accepting next", { holdError });
        alert(`Failed to hold current call: ${(holdError as Error).message}. Cannot accept next call.`);
        return;
      }
    }

    setIsAcceptingCall(true); // Indicate process started
    logInfo("acceptNextCall", "Attempting to accept next call", { assetId });

    try {
      // dequeueAndConnectCallAPI will now set up handlers that will call setCurrentActiveCall on 'accept'
      await dequeueAndConnectCallAPI(); 
      // If dequeueAndConnectCallAPI resolves without error, it means the connection process 
      // has been initiated and event handlers are set up to manage subsequent state.
      logInfo("acceptNextCall", "Call acceptance process initiated and handed over to SDK event handlers.");
    } catch (error) {
      const err = error as Error;
      logError("acceptNextCall", "Failed to accept call", { error: err });
      if (err.message === 'No calls available in queue') {
        alert('No calls available to accept.'); 
      } else {
        alert(`Error accepting call: ${err.message}`); 
      }
      setIsAcceptingCall(false); // Critical to reset on error
      setCallStatus('idle');   // Reset call status
      invalidateCommonQueries(); // Good to refresh on error
    }
  };

  // Hold the current active call
  const holdCurrentCall = async () => {
    if (!currentActiveCall) {
      throw new Error("No active call to hold.");
    }

    // For outbound call we don't have hold feature 
    // TODO: change once things get implemented
    if (currentActiveCall.attributes?.direction === 'outbound') {
      await endCurrentCall();
      return true;
    }

    const startTime = Date.now();
    const callSid = currentActiveCall.callSid;
    
    logInfo("holdCurrentCall", "Holding current call", { callSid });
    
    try {
      const currentConnection = activeAgentLegInstanceRef.current[callSid];
      await placeCallOnHoldAPI(callSid, currentConnection);
      
      const duration = Date.now() - startTime;
      
      // Track successful hold
      trackSentryCallOperation({
        callSid,
        assetId: assetId || 'unknown',
        operation: 'hold_call_success',
        duration
      });
      
      trackSentryPerformance('hold_call', duration);
      
      setCurrentlyHeldCall(currentActiveCall);
      setCurrentActiveCall(null);
      setIsMuted(false);
      refetchHeldCalls();
      logInfo("holdCurrentCall", "Call held successfully", { callSid });
      return true;
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = (error as Error).message;
      
      logError("holdCurrentCall", "Failed to hold current call", { error });
      
      // Track hold failure
      trackSentryCallOperation({
        callSid,
        assetId: assetId || 'unknown',
        operation: 'hold_call_failed',
        errorType: 'hold_api_error',
        errorMessage,
        duration
      });
      
      throw error;
    }
  };

  // Resume the held call
  const resumeCurrentCall = async () => {
    if (currentActiveCall) {
      logInfo("resumeSpecificCall", "Active call detected; placing current call on hold", {
        currentActiveCall,
      });
      await holdCurrentCall();
    }
    if (!currentlyHeldCall) {
      throw new Error("No held call to resume.");
    }
    
    const startTime = Date.now();
    const callSid = currentlyHeldCall.callSid;
    
    logInfo("resumeCurrentCall", "Resuming held call", { callSid });
    setIsResumingCall(true);
    
    try {
      await resumeCallAPI(callSid);
      const duration = Date.now() - startTime;
      
      logInfo("resumeCurrentCall", "Resume initiated via resumeCallAPI", { callSid });
      
      // Track successful resume
      trackSentryCallOperation({
        callSid,
        assetId: assetId || 'unknown',
        operation: 'resume_call_success',
        duration
      });
      
      trackSentryPerformance('resume_call', duration);
      
      setCurrentActiveCall(currentlyHeldCall);
      setCurrentlyHeldCall(null);
      setIsResumingCall(false);
      return true;
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = (error as Error).message;
      
      logError("resumeCurrentCall", "Failed to resume call", { error });
      
      // Track resume failure
      trackSentryCallOperation({
        callSid,
        assetId: assetId || 'unknown',
        operation: 'resume_call_failed',
        errorType: 'resume_api_error',
        errorMessage,
        duration
      });
      
      setIsResumingCall(false);
      throw error;
    }
  };

  // Resume a specific call
  const resumeSpecificCall = async (callToResume: QueuedCall) => {
    if (currentActiveCall) {
      logInfo("resumeSpecificCall", "Active call detected; placing current call on hold", {
        currentActiveCall,
      });
      await holdCurrentCall();
    }
    logInfo("resumeSpecificCall", "Resuming call", { callToResume });
    try {
      await resumeCallAPI(callToResume.callSid);
      logInfo("resumeSpecificCall", "Call resumed successfully via resumeCallAPI", { callToResume });
      setCurrentActiveCall(callToResume);
      if (currentlyHeldCall?.callSid === callToResume.callSid) {
        setCurrentlyHeldCall(null);
      }
      refetchHeldCalls();
      return true;
    } catch (error) {
      logError("resumeSpecificCall", "Error resuming call", { error });
      throw error;
    }
  };

  // End the current call (active or held)
  const endCurrentCall = async () => {
    const startTime = Date.now();
    
    if (!currentActiveCall) {
      if (currentlyHeldCall) {
        const callSid = currentlyHeldCall.callSid;
        logInfo("endCurrentCall", "No active call found, but held call exists. Ending held call instead", {
          callSid,
        });
        try {
          const heldCallConnection = activeAgentLegInstanceRef.current[callSid];
          await endCallAPI(callSid, heldCallConnection);
          
          const duration = Date.now() - startTime;
          
          // Track successful end of held call
          trackSentryCallOperation({
            callSid,
            assetId: assetId || 'unknown',
            operation: 'end_held_call_success',
            duration
          });
          
          trackSentryPerformance('end_call', duration);
          
          invalidateCommonQueries();
          logInfo("endCurrentCall", "Held call ended successfully", { callSid });
          setCurrentlyHeldCall(null);
          setIsMuted(false);
          return true;
        } catch (error) {
          const duration = Date.now() - startTime;
          const errorMessage = (error as Error).message;
          
          logError("endCurrentCall", "Failed to end held call", { error });
          
          const err = error as Error;
          if (
            err.message?.includes('Connection already disconnected') ||
            err.message?.includes('Cannot disconnect')
          ) {
            logInfo("endCurrentCall", "Connection already disconnected, updating UI state anyway");
            
            // Track as successful cleanup
            trackSentryCallOperation({
              callSid,
              assetId: assetId || 'unknown',
              operation: 'end_held_call_success',
              duration
            });
            
            setCurrentlyHeldCall(null);
            setIsMuted(false);
            return true;
          } else {
            // Track end call failure
            trackSentryCallOperation({
              callSid,
              assetId: assetId || 'unknown',
              operation: 'end_held_call_failed',
              errorType: 'end_api_error',
              errorMessage,
              duration
            });
            
            alert(`Could not end held call: ${err.message}`);
          }
          return false;
        }
      } else {
        console.error('No active or held call to end');
        return false;
      }
    }
    
    const callSid = currentActiveCall.callSid;
    logInfo("endCurrentCall", "Attempting to end call", { callSid });
    
    try {
      const currentConnection = activeAgentLegInstanceRef.current[callSid];
      await endCallAPI(callSid, currentConnection);
      
      const duration = Date.now() - startTime;
      
      // Track successful end call
      trackSentryCallOperation({
        callSid,
        assetId: assetId || 'unknown',
        operation: 'end_call_success',
        duration
      });
      
      trackSentryPerformance('end_call', duration);
      
      invalidateCommonQueries();
      logInfo("endCurrentCall", "Call ended successfully", { callSid });
      setCurrentActiveCall(null);
      setIsMuted(false);
      return true;
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = (error as Error).message;
      
      logError("endCurrentCall", "Failed to end call", { error });
      
      const err = error as Error;
      if (
        err.message?.includes('Connection already disconnected') ||
        err.message?.includes('Cannot disconnect')
      ) {
        logInfo("endCurrentCall", "Connection already disconnected, updating UI state anyway");
        
        // Track as successful cleanup
        trackSentryCallOperation({
          callSid,
          assetId: assetId || 'unknown',
          operation: 'end_call_success',
          duration
        });
        
        if (currentActiveCall.callSid == currentlyHeldCall?.callSid) {
          setCurrentlyHeldCall(null);
        }
        setCurrentActiveCall(null);
        setIsMuted(false);
        return true;
      } else {
        // Track end call failure
        trackSentryCallOperation({
          callSid,
          assetId: assetId || 'unknown',
          operation: 'end_call_failed',
          errorType: 'end_api_error',
          errorMessage,
          duration
        });
        
        alert(`Could not end call: ${err.message}`);
      }
      return false;
    }
  };

  // Toggle mute for the current call
  const muteCurrentCall = (): boolean => {
    if (!currentActiveCall) {
      console.error('No active call to mute');
      return false;
    }
    const currentConnection = activeAgentLegInstanceRef.current[currentActiveCall.callSid];
    const newMuteState = !isMuted;
    logInfo("muteCurrentCall", "Toggling mute for current call", { newMuteState });
    const result = toggleMute(currentConnection, newMuteState);
    if (result) {
      setIsMuted(newMuteState);
      logInfo("muteCurrentCall", `Call ${newMuteState ? "muted" : "unmuted"} successfully`, {
        callStatus: newMuteState ? "muted" : "active",
      });
    }
    return result;
  };

  // If a held call is found for a specific situationId, set it as the currently held call
  const checkAndAddHeldCallForSituation = async (situationId: string) => {
    try {
      const heldCallsResponse = await refetchHeldCalls();
      const heldCalls = heldCallsResponse.data?.heldCalls;
      
      if (!heldCalls) {
        logInfo("checkAndAddHeldCallForSituation", "No held calls available to check", { situationId });
        setCurrentlyHeldCall(null);
        return;
      }
      
      const foundCall = heldCalls.find((call) => call.situationId === situationId);
      if (foundCall) {
        // Only update if the call is different from what's already set.
        if (!currentlyHeldCall || currentlyHeldCall.callSid !== foundCall.callSid) {
          logInfo("checkAndAddHeldCallForSituation", "Found held call matching situationId", { situationId, foundCall });
          setCurrentlyHeldCall(foundCall);
        }
      } else {
        logInfo("checkAndAddHeldCallForSituation", "No held call found for situationId", { situationId });
        setCurrentlyHeldCall(null);
      }
    } catch (error) {
      logError("checkAndAddHeldCallForSituation", "Error fetching held calls data", { error, situationId });
      // Consider what should happen on failure - perhaps use the cached data as fallback
      if (heldCalls && heldCalls) {
        const cachedFoundCall = heldCalls.heldCalls?.find((call) => call.situationId === situationId);
        if (cachedFoundCall) {
          logInfo("checkAndAddHeldCallForSituation", "Using cached data after fetch failure", { situationId });
          setCurrentlyHeldCall(cachedFoundCall);
        }
      }
    }
  };

  // ---------------------------------------------------------------------------
  // Bundle Context Value & Render Provider
  // ---------------------------------------------------------------------------
  const contextValue: CallContextType = {
    currentActiveCall,
    currentlyHeldCall,
    isMuted,
    token,
    tokenExpiry,
    isAcceptingCall,
    isResumingCall,
    isConnecting,
    device,
    microphoneAccessFailed,
    deviceStatus,
    deviceError,
    isMicrophoneEnabled,
    callStatus,
    queueStatus,
    heldCalls,
    setCurrentlyHeldCall,
    acceptNextCall,
    holdCurrentCall,
    resumeCurrentCall,
    resumeSpecificCall,
    endCurrentCall,
    muteCurrentCall,
    checkAndAddHeldCallForSituation,
    makeOutboundCall,
    dequeueSpecificCall,
  };

  return (
    <CallContext.Provider value={contextValue}>
      {children}
    </CallContext.Provider>
  );
};

// -----------------------------------------------------------------------------
// Custom Hook to Access Call Context
// -----------------------------------------------------------------------------
export const useCallContext = (): CallContextType => {
  const context = useContext(CallContext);
  if (!context) {
    throw new Error('[CallContext] useCallContext must be used within a CallProvider');
  }
  return context;
};
