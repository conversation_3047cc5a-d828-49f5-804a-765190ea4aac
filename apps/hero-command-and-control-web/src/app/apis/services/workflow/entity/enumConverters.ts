import { EntityType, RecordStatus } from "proto/hero/entity/v1/entity_pb";

// Function to convert a string (e.g. "ENTITY_TYPE_PERSON")
// to the corresponding enum value (e.g. EntityType.PERSON).
export function stringToEntityType(value: string): EntityType | undefined {
    const prefix = "ENTITY_TYPE_";
    if (!value.startsWith(prefix)) {
        return undefined;
    }
    // Remove the prefix to get the key matching the enum property name.
    const enumKey = value.substring(prefix.length) as keyof typeof EntityType;
    // Ensure the key exists in the enum.
    if (enumKey in EntityType) {
        return EntityType[enumKey];
    }
    return undefined;
}

// Function to convert an enum value (e.g. EntityType.PERSON)
// back to its string representation (e.g. "ENTITY_TYPE_PERSON").
export function entityTypeToString(entityType: EntityType): string {
    // Using reverse mapping: EntityType[entityType] returns the key as a string.
    const key = EntityType[entityType];
    return "ENTITY_TYPE_" + key;
}

// For hooks that return string representations of enums rather than the enum values
export function hookEntityTypeToString(entityType: EntityType): string {
    return String(entityType);
}

// Function to convert a string (e.g. "RECORD_STATUS_ACTIVE")
// to the corresponding enum value (e.g. RecordStatus.ACTIVE).
export function stringToRecordStatus(value: string): RecordStatus | undefined {
    const prefix = "RECORD_STATUS_";
    if (!value.startsWith(prefix)) {
        return undefined;
    }
    // Remove the prefix to get the enum key.
    const enumKey = value.substring(prefix.length) as keyof typeof RecordStatus;
    if (enumKey in RecordStatus) {
        return RecordStatus[enumKey];
    }
    return undefined;
}

// Function to convert an enum value (e.g. RecordStatus.ACTIVE)
// back to its string representation (e.g. "RECORD_STATUS_ACTIVE").
export function recordStatusToString(status: RecordStatus): string {
    const key = RecordStatus[status];
    return "RECORD_STATUS_" + key;
}

// For hooks that return string representations of enums rather than the enum values
export function hookRecordStatusToString(status: RecordStatus): string {
    return String(status);
} 