import * as Sentry from "@sentry/nextjs";

// Sentry Client Config to handle browser-based instrumentation (user interactions, client errors, session replay)

// Determine if we should initialize Sentry
// In production, Next.js config handles initialization via withSentryConfig
// In development, only initialize if NEXT_PUBLIC_ENABLE_SENTRY is true in .env.local
const isProduction = process.env.NODE_ENV === 'production';
const enableSentryInDev = process.env.NEXT_PUBLIC_ENABLE_SENTRY === 'true';

if (isProduction || enableSentryInDev) {
  Sentry.init({
    dsn: "https://<EMAIL>/4509079520477185",
    environment: isProduction ? "production" : "development",

    integrations: [
      Sentry.browserTracingIntegration(),
      Sentry.replayIntegration({
        maskAllText: false,
        maskAllInputs: false,
        blockAllMedia: false,
      }),
      Sentry.replayCanvasIntegration(),
    ],

    tracesSampleRate: isProduction ? 0.1 : 1, // 10% for production, 100% for development
    replaysSessionSampleRate: 0.1,
    replaysOnErrorSampleRate: 1.0,
    debug: enableSentryInDev, // Enable debug logs in development testing
  });
}